import { PrismaMongoClient } from '../packages/model/mongodb/prisma'
import axios from 'axios'
import { Config } from '../packages/config'
import { loadConfigByWxId } from '../packages/model/bot_config/load_config'
import { YuHeValidator } from '../apps/yuhe/helper/validator/validator'
import { YuheDataService } from '../apps/yuhe/helper/getter/get_data'
import { ChatStateStore } from '../packages/service/local_cache/chat_state_store'
import { IChattingFlag } from '../apps/yuhe/state/user_flags'
import { Queue } from 'bullmq'
import { RedisDB } from '../packages/model/redis/redis'
import { exec } from 'child_process'

describe('Test', function () {
  beforeAll(() => {

  })

  it('', async () => {
    // yuhe_import_white_list
    const queue = new Queue('yuhe_import_white_list', {
      connection: RedisDB.getInstance()
    })

    await queue.add(
      'checkPrevDay',
      { only_check: true, prevDay: true },
      {
        repeat: { pattern: '0 12 * * *' }, // 每天中午12点执行
        jobId: 'checkPrevDayJob1',
      }
    )

    await queue.add(
      'checkPrevDay',
      { only_check: true, prevDay: true },
      {
        repeat: { pattern: '0 14 * * *' }, // 每天下午2点执行
        jobId: 'checkPrevDayJob2',
      }
    )

    console.log(JSON.stringify(await queue.getRepeatableJobs(), null, 4))

    // await queue.add(
    //   'dailyImportWhiteList',
    //   { timestamp: Date.now() },
    //   {
    //     repeat: { pattern: '0 0 * * *' }, // 每天 0 点执行
    //     jobId: 'dailyImportWhiteList', // 固定的 jobId 确保只有一个任务
    //   }
    // )
  }, 60000)

  it('添加账号配置', async () => {
    // 添加 docker 配置, 推送代码

    const configs = [
      //9597cb6c249920c882c609efcf8ac219(****************)
      //ChenXingXu(****************)
      // 企业账号是可以重叠的
      {
        'enterpriseName': 'yuhe',
        'accountName': 'yuhe10',
        'wechatId': '****************',
        'address': 'http://***************:5010',
        'port': '5010',
        'botUserId': 'LiuZiJian_1',
        'orgToken': '6801c818f8b463b1d228f23d',
        'enterpriseConfig': {
          'notifyGroupId': 'R:*****************',
        }
      },
      {
        'enterpriseName': 'yuhe',
        'accountName': 'yuhe11',
        'wechatId': '****************',
        'address': 'http://***************:5011',
        'port': '5011',
        'botUserId': 'NiSiDian',
        'orgToken': '6801c818f8b463b1d228f23d',
        'enterpriseConfig': {
          'notifyGroupId': 'R:*****************',
        }
      },
    ]

    await PrismaMongoClient.getConfigInstance().config.createMany({
      data: configs
    })

    // log docker 配置
    for (const config of configs) {
      console.log(`${config.accountName}:
    image: crpi-5ht6itmmkqtumodb.cn-hangzhou.personal.cr.aliyuncs.com/freespirit/yuhe:latest
    container_name: ${config.accountName}
    environment:
    - NODE_ENV=dev
    - TEACHER_NAME=中神通
    - TZ=Asia/Shanghai
    - WECHAT_NAME=${config.accountName}
    ports:
      - "${config.port}:${config.port}"
    restart: always`)

      console.log('')
    }

    console.log('code apps/yuhe/docker/docker-compose.yaml')

    const res = await axios.post('http://*************:6001/api/clear-server-address-cache')
    console.log(JSON.stringify(res.data, null, 4))

    // YuHe 事件转发缓存清除

    // 提醒提交代码
    console.log('提交代码，更新 docker 配置')
  }, 60000)

  it('导入白名单', async () => {
    Config.setting.wechatConfig = await loadConfigByWxId('****************') // 挂个配置，用于发送群通知消息

    // 1. 分组配置，校验直播间配置，进阶课配置
    await YuHeValidator.validateLiveStream()

    // 2. 导入白名单
    await YuheDataService.importWhiteListOfYesterdayUsers()
  }, 60000)

  it('ce', async () => {
    const flags = await ChatStateStore.getFlags<IChattingFlag>('7881303126308651_****************')
    console.log(flags.is_in_live_room ?? false)
  }, 60000)


})