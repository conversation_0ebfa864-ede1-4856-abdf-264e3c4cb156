#!/usr/bin/env bash
# daily_commit_diffs.sh
# ---------------------------------------------
# 将今天 (本地午夜以来) 的所有 commit 及其 diff
# 写入同一个文件： daily_commit_diffs.txt
# 格式：
#   <commitSHA> :
#   <完整 diff 内容>
#   ----
#   <下一个 commitSHA> :
#   ...
#
# 用法：
#   ./daily_commit_diffs.sh            # 在当前仓库根目录执行
#   ./daily_commit_diffs.sh /path/repo # 或指定仓库路径
# ---------------------------------------------
set -euo pipefail

# 目标仓库（默认为当前目录）
REPO="${1:-.}"
OUTPUT_FILE="daily_commit_diffs.txt"

# 计算今日 00:00 本地时间 (兼容 GNU date 与 BSD/macOS date)
if date -d "today 00:00" +%s >/dev/null 2>&1; then
  MIDNIGHT=$(date -d "today 00:00" +"%Y-%m-%dT%H:%M:%S")
else
  MIDNIGHT=$(date -v0H -v0M -v0S +"%Y-%m-%dT%H:%M:%S")
fi

echo "Collecting commits since $MIDNIGHT from $REPO ..."

cd "$REPO" || { echo "Error: repo path not found."; exit 1; }

COMMITS=$(git log --since="$MIDNIGHT" --pretty=format:%H)

if [[ -z "$COMMITS" ]]; then
  echo "No commits since midnight." > "$OUTPUT_FILE"
  echo "No commits found. Exiting."
  exit 0
fi

# 清空输出文件
: > "$OUTPUT_FILE"

for SHA in $COMMITS; do
  echo "Processing $SHA"
  {
    echo "$SHA :"
    git show "$SHA"
    echo -e "\n----\n"
  } >> "$OUTPUT_FILE"
done

echo "Done. Combined diffs written to $(pwd)/$OUTPUT_FILE"
