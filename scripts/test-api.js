/**
 * 测试API功能
 */

const { PrismaClient } = require('@prisma/client')

// 模拟API函数
async function getDashboardTagsByIds(tagIds) {
  const prisma = new PrismaClient()
  const tags = await prisma.dashboard_tag.findMany({
    where: {
      id: {
        in: tagIds
      }
    }
  })
  await prisma.$disconnect()
  return tags
}

async function mergeDashboardDataWithChatHistory(dashboard) {
  const prisma = new PrismaClient()
  const chatHistoryIdSet = new Set()
  const tagIdSet = new Set()
  
  for (const item of dashboard) {
    for (const id of item.chat_history_id) {
      chatHistoryIdSet.add(id)
    }
    for (const tagId of item.tag_ids) {
      tagIdSet.add(tagId)
    }
  }
  
  const chatHistory = await prisma.chat_history.findMany({ 
    where: { id: { in: [...chatHistoryIdSet] } }, 
    select: { id: true, chat_id: true, content: true, created_at: true, role: true } 
  })
  
  const chatHistoryMap = new Map()
  for (const item of chatHistory) {
    chatHistoryMap.set(item.id, item)
  }
  
  const tags = await getDashboardTagsByIds([...tagIdSet])
  const tagMap = new Map()
  for (const tag of tags) {
    tagMap.set(tag.id, tag)
  }
  
  await prisma.$disconnect()
  
  return dashboard.map((item) => {
    return {
      ...item,
      chat_history: item.chat_history_id.map((singleChatHistory) => {
        return chatHistoryMap.get(singleChatHistory)
      }).filter((singleChatHistory) => singleChatHistory != undefined).sort((a, b) => a.created_at.getTime() - b.created_at.getTime()),
      tags: item.tag_ids.map(tagId => tagMap.get(tagId)).filter(tag => tag != undefined)
    }
  })
}

async function testAPI() {
  const prisma = new PrismaClient()
  
  try {
    console.log('测试API功能...')
    
    // 获取一条dashboard数据
    const dashboard = await prisma.dashboard_data.findMany({ take: 1 })
    console.log('原始数据:', JSON.stringify(dashboard[0], null, 2))
    
    // 测试合并函数
    const merged = await mergeDashboardDataWithChatHistory(dashboard)
    console.log('\n合并后数据:')
    console.log('- ID:', merged[0].id)
    console.log('- tag_ids:', merged[0].tag_ids)
    console.log('- tags:', merged[0].tags.map(tag => ({ id: tag.id, name: tag.name, color: tag.color })))
    console.log('- chat_history count:', merged[0].chat_history.length)
    
  } catch (error) {
    console.error('测试失败:', error)
  } finally {
    await prisma.$disconnect()
  }
}

testAPI()
