/**
 * 测试随机颜色标签创建
 */

const { PrismaClient } = require('@prisma/client')

async function testRandomColors() {
  const prisma = new PrismaClient()
  
  try {
    console.log('测试创建随机颜色标签...')
    
    // 创建几个测试标签
    const testTags = ['测试标签1', '测试标签2', '测试标签3', '测试标签4', '测试标签5']
    
    for (const tagName of testTags) {
      // 检查是否已存在
      const existing = await prisma.dashboard_tag.findFirst({
        where: { name: tagName }
      })
      
      if (!existing) {
        // 生成随机浅色
        const lightColors = [
          '#E3F2FD', '#F3E5F5', '#E8F5E8', '#FFF3E0', '#FCE4EC',
          '#F1F8E9', '#FFF8E1', '#EFEBE9', '#E0F2F1', '#F9FBE7',
          '#EDE7F6', '#E1F5FE', '#FFF9C4', '#FFEBEE', '#F0F4C3'
        ]
        
        const randomColor = lightColors[Math.floor(Math.random() * lightColors.length)]
        
        const tag = await prisma.dashboard_tag.create({
          data: {
            name: tagName,
            color: randomColor
          }
        })
        
        console.log(`创建标签: ${tagName}, 颜色: ${randomColor}`)
      } else {
        console.log(`标签已存在: ${tagName}, 颜色: ${existing.color}`)
      }
    }
    
    // 显示所有标签
    const allTags = await prisma.dashboard_tag.findMany()
    console.log('\n所有标签:')
    allTags.forEach(tag => {
      console.log(`- ${tag.name}: ${tag.color}`)
    })
    
  } catch (error) {
    console.error('测试失败:', error)
  } finally {
    await prisma.$disconnect()
  }
}

testRandomColors()
