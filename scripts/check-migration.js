/**
 * 检查迁移结果
 */

const { PrismaClient } = require('@prisma/client')

async function checkMigration() {
  const prisma = new PrismaClient()
  
  try {
    console.log('检查迁移结果...')
    
    // 检查标签表
    const tags = await prisma.dashboard_tag.findMany()
    console.log(`\n标签表 (${tags.length} 条记录):`)
    tags.forEach(tag => {
      console.log(`- ID: ${tag.id}, 名称: ${tag.name}, 颜色: ${tag.color}`)
    })
    
    // 检查dashboard_data表
    const dashboardData = await prisma.dashboard_data.findMany({
      take: 3 // 只取前3条
    })
    
    console.log(`\ndashboard_data表 (显示前3条):`)
    dashboardData.forEach(data => {
      console.log(`- ID: ${data.id}`)
      console.log(`  tag_ids: ${JSON.stringify(data.tag_ids)}`)
      console.log(`  description: ${data.description.substring(0, 50)}...`)
      console.log('')
    })
    
    // 测试查询带标签的数据
    console.log('测试查询功能...')
    
    // 获取所有标签ID
    const tagIds = tags.map(tag => tag.id)
    
    // 查询包含任意标签的数据
    if (tagIds.length > 0) {
      const dataWithTags = await prisma.dashboard_data.findMany({
        where: {
          tag_ids: {
            hasSome: tagIds
          }
        },
        take: 2
      })
      
      console.log(`包含标签的数据 (${dataWithTags.length} 条):`)
      dataWithTags.forEach(data => {
        console.log(`- ID: ${data.id}, 标签数量: ${data.tag_ids.length}`)
      })
    }
    
  } catch (error) {
    console.error('检查失败:', error)
  } finally {
    await prisma.$disconnect()
  }
}

checkMigration()
