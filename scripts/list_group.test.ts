import { JuziAPI } from '../packages/lib/juzi/api'
import { Config } from '../packages/config'
import { loadConfigByWxId } from '../packages/model/bot_config/load_config'
import { GroupNotification } from '../packages/service/group_notification/group_notification'

describe('Test', function () {
  beforeAll(() => {

  })

  it('获取群 id', async () => {
    Config.setting.wechatConfig = await loadConfigByWxId('1688858335726355')


    await GroupNotification.notify('AI 提醒测试', 'R:10933256292171603')


    // const ids = ['1688858335726355']
    //
    // for (const id of ids) {
    //   const rooms =  await JuziAPI.listGroup(id)
    //
    //   for (const room of rooms) {
    //     console.log(room.name, room.imRoomId, room.owner === id, room.owner)
    //   }
    // }
  }, 60000)
})