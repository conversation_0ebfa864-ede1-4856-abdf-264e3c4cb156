import { YuHeWorkflow } from '../yuhe/workflow/workflow'
import { Config } from '../../packages/config'
import { WorkFlow } from './workflow'

/**
 * WorkFlow Interface 定义， 各个服务商 WorkFlow 配置定义
 */
export enum ProjectName {
  YuHe = 'yuhe'
}

interface IWorkflowConfig {
  name: string
  avatarName: string
  workflow: typeof WorkFlow
}


export const workflowConfig:  { [key in ProjectName]: IWorkflowConfig} = {
  [ProjectName.YuHe]: {
    name: ProjectName.YuHe,
    avatarName: '大麦老师',
    workflow: YuHeWorkflow
  }
}

// 根据注入的项目名称，返回对应的 WorkFlow
export function getWorkflowByProjectName(): typeof WorkFlow {
  // 读取环境变量
  const projectName = Config.setting.enterpriseName as ProjectName

  return workflowConfig[projectName].workflow
}
