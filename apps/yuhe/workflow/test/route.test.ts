import { getUserId } from '../../../../packages/config/chat_id'
import { isInClassTime, YuHeEventHandler } from '../../client/event_handler'
import { NodeRouter } from '../route'
import { JuziAPI } from '../../../../packages/lib/juzi/api'
import { Config } from '../../../../packages/config'
import { loadConfigByAccountName } from '../../../../packages/service/database/config'

describe('Test', function () {
  beforeAll(() => {

  })

  it('test robot detection', async () => {
    const chat_id = '7881302298050442_1688857404698934'
    const user_id = getUserId(chat_id)
    const userMessage = '老师，你他妈就是AI吧'
    const isRobotDetection = await NodeRouter.checkRobotDetection(chat_id, '', user_id, userMessage)
    console.log('isRobotDetection', isRobotDetection)
  }, 60000)

  it('test invite to group', async () => {
    JuziAPI.wxIdToExternalUserId = async (user_id) => {
      return '11'
    }
    Config.setting.wechatConfig = await loadConfigByAccountName('yuhe1')
    await YuHeEventHandler.inviteToGroup('7881302298050442_1688857404698934', '7881302298050442_1688857404698934')
  }, 60000)
})
