export enum YuHeNode {
    Dummy = 'dummy', // 占位假节点，在路由的时候 return 这个节点，表示在路由期间直接执行逻辑，不再进行后续路由判断。会继续执行上次的节点
    DummyEnd = 'dummy_end', // 占位假节点，在路由的时候 return 这个节点，表示在路由期间已经执行完所有逻辑，不执行后续节点的逻辑
    FreeTalk = 'free_talk', // 闲聊节点
    PhoneQuery = 'phone_query',
    SendFile = 'send_file', // 发送文件节点
    IntentionQuery = 'intention_query', // 挖需节点
    DouyinAnalysis = 'douyin_analysis', // 抖音分析节点
    Homework1 = 'homework1',
    Homework2 = 'homework2',
}