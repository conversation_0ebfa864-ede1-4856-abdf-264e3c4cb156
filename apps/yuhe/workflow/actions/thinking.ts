import { IWorkflowState } from '../../../../packages/service/llm/state'
import { YuHeContextManager } from '../context/context_manager'
import { ChatHistoryService } from '../../../../packages/service/chat_history/chat_history'
import { SystemMessagePromptTemplate } from '@langchain/core/prompts'
import { LLM } from '../../../../packages/lib/ai/llm/LLM'
import { XMLHelper } from '../../../../packages/lib/xml/xml'
import { EventTracker, IEventType } from '../../../../packages/model/logger/data_driven'
import logger from '../../../../packages/model/logger/logger'
import { MetaActionRouter } from '../../meta_action/meta_action_router'

interface LLMThinkingOptions {
  model?: string
  temperature?: number
  historyRound?: number // 聊天记录轮数
  notHandleAction?: boolean // 不执行动作
}

export class YuHeThinking {
  public static async invoke(state: IWorkflowState, thinkPrompt: string, metaActions: string, options?: LLMThinkingOptions) {
    const historyCount = options?.historyRound || 6
    const modelName = options?.model || 'gpt-4.1'
    const temperature = options?.temperature || 0.8

    const userBehavior = await YuHeContextManager.getUserBehaviors(state.chat_id)
    const userPortrait = await YuHeContextManager.getUserSlots(state.chat_id)
    const chatHistory = await ChatHistoryService.getFilteredChatHistory(state.chat_id, historyCount, 18)

    const freeThinkPrompt = SystemMessagePromptTemplate.fromTemplate(`# 角色设定
- 你是顶级销售，擅长根据对话记录，客户画像与客户行为来深度思考以完成目的
- 性格高冷，目标导向，敏锐洞察客户心理，不会无效夸赞，避免过度共情
- 询问时就不要使用其他陈述类元行为，专注询问即可
- 要关注对话记录的整体效果，客户的态度变化，避免行为模式的过度重复，避免过度询问

## 主要任务
- 思考（think）{{thinkPrompt}}
- 行为（action）只包含 think 中提到的元行为名字，按顺序，中间用逗号“,”隔开
- 策略（strategy）根据 think 与 action，结合所选元行为中的通用内容，输出针对当前状态最优的指导策略
- 话术（content）输出要简单直接

## 元行为
{{metaActions}}

{{userBehavior}}

{{userPortrait}}

## 对话记录
{{chatHistory}}

## 格式要求
- 请先将思考输出到 <think></think> 标签中
- 然后将行为输出到 <action></action> 标签中
- 再后将策略输出到 <strategy></strategy> 标签中
- 最后将话术输出到 <content></content> 标签中`,
    { templateFormat: 'mustache' })


    const result = await LLM.predict(freeThinkPrompt,
      {
        model: modelName,
        temperature: temperature,
        meta: {
          promptName: 'think_strategy',
          chat_id: state.chat_id,
          round_id: state.round_id
        }
      },
      {
        thinkPrompt: thinkPrompt,
        metaActions: metaActions,
        userBehavior: userBehavior,
        userPortrait: userPortrait,
        chatHistory: chatHistory,
      }
    )

    const think = XMLHelper.extractContent(result, 'think') || ''
    const action = XMLHelper.extractContent(result, 'action') || ''
    const strategy = XMLHelper.extractContent(result, 'strategy') || ''
    const content = XMLHelper.extractContent(result, 'content') || ''

    const actionInfo = await MetaActionRouter.handleAction(state.chat_id, state.round_id, action)

    EventTracker.track(state.chat_id, IEventType.thinkStrategy, { round_id: state.round_id, think: think, action: action, strategy: strategy, content: content })
    logger.debug({ chat_id: state.chat_id, round_id: state.round_id }, `think: ${think}\naction: ${action}\nstrategy: ${strategy}`)

    return { think, actionInfo, strategy, content, action }
  }
}