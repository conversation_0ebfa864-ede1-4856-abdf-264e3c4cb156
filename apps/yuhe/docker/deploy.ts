#!/usr/bin/env ts-node
/**
 * 部署打包脚本 - YUHE 项目
 *
 * 支持：
 *  - 通过 inquirer 输入版本号，如果输入为空则自动生成当前日期时间作为版本号
 *  - 交互式多选服务进行部署（使用 inquirer 的 checkbox）
 *  - 构建、标记、推送单个镜像供所有服务使用
 *  - 通过 SSH 触发远程部署
 */
import * as fs from 'fs'
import { execSync } from 'child_process'
import inquirer from 'inquirer'
import chalk from 'chalk'
import yaml from 'js-yaml'

// Define the type for the parsed docker-compose structure
interface DockerCompose {
  services: Record<string, any>
}

// ===== 配置变量 =====
const IMAGE_NAME = 'yuhe' // 本地构建时使用的镜像名
const REMOTE_REPO = 'crpi-5ht6itmmkqtumodb.cn-hangzhou.personal.cr.aliyuncs.com/freespirit/yuhe' // 修改为 yuhe 的仓库路径
const LOG_FILE = './deploy_script.log' // 日志文件路径（在 docker 目录下）
const DOCKER_COMPOSE_PATH = './apps/yuhe/docker/docker-compose.yaml' // docker-compose 文件相对路径

const REMOTE_DEPLOY_SCRIPT = './server_deploy.sh' // 服务器上的部署脚本名称
const REMOTE_USER_HOST = 'root@***************' // SSH 目标地址
const REMOTE_PROJECT_PATH = '/root/eliza-sales' // !!! 更新为服务器上 yuhe 项目的实际路径 !!!

// ===== 工具函数 =====

/**
 * 从 docker-compose 文件读取可用服务列表
 */
function getAvailableServices(): string[] {
  try {
    const doc: DockerCompose = yaml.load(fs.readFileSync(DOCKER_COMPOSE_PATH, 'utf8')) as DockerCompose
    if (doc && doc.services) {
      return Object.keys(doc.services)
    }
    log(`错误: 在 ${DOCKER_COMPOSE_PATH} 中找不到 services`, 'red')
    process.exit(1)
  } catch (error) {
    if (error instanceof Error) {
      log(`错误：读取或解析 ${DOCKER_COMPOSE_PATH} 出错: ${error.message}`, 'red')
    } else {
      log(`错误：读取或解析 ${DOCKER_COMPOSE_PATH} 出错 ${error}`, 'red')
    }
    process.exit(1)
  }
}

/**
 * 获取默认版本号：格式为 YYYY-MM-DD.HH-MM-SS
 */
function getDefaultVersion(): string {
  const now = new Date()
  const year = now.getFullYear()
  const month = String(now.getMonth() + 1).padStart(2, '0')
  const day = String(now.getDate()).padStart(2, '0')
  const hours = String(now.getHours()).padStart(2, '0')
  const minutes = String(now.getMinutes()).padStart(2, '0')
  const seconds = String(now.getSeconds()).padStart(2, '0')
  return `${year}-${month}-${day}.${hours}-${minutes}-${seconds}`
}

/**
 * 日志函数，将日志同时输出到控制台和日志文件中
 * @param message 要记录的日志信息
 * @param color chalk 颜色名称 (可选)
 */
function log(message: string, color: keyof typeof chalk = 'white') {
  const timestamp = new Date().toISOString().replace('T', ' ').substring(0, 19)
  // 动态使用 chalk 颜色，如果颜色无效则默认为白色
  // @ts-ignore fku chalk
  const coloredMessage = chalk[color] ? chalk[color](`[${timestamp}] ${message}`) : `[${timestamp}] ${message}`
  const plainMessage = `[${timestamp}] ${message}` // 用于文件记录的纯文本消息
  try {
    fs.appendFileSync(LOG_FILE, `${plainMessage}\n`)
  } catch (err) {
    // @ts-ignore 123
    console.error(`写入日志文件 ${LOG_FILE} 出错: ${err.message}`)
  }
  console.log(coloredMessage)
}

/**
 * 执行 shell 命令，记录命令，并在失败时退出
 * @param command 命令字符串
 */
function runCommand(command: string) {
  log(`执行命令: ${command}`, 'gray') // 使用中文日志
  try {
    // stdio: 'inherit' 将命令输出直接显示在控制台
    execSync(command, { stdio: 'inherit' })
  } catch (error) {
    log(`命令执行失败: ${command}`, 'red') // 使用中文日志
    // error 对象可能包含更多细节，但 execSync 通常信息不多
    // @ts-ignore 123
    log(`错误详情: ${error.message || error}`, 'red') // 使用中文日志
    process.exit(1)
  }
}

// ===== 主流程 =====
async function main() {
  log('===== 开始 YUHE 部署流程 =====', 'cyan') // 中文日志

  // 每次部署开始时清空日志文件 (可选)
  if (fs.existsSync(LOG_FILE)) {
    fs.writeFileSync(LOG_FILE, '')
  }

  // --- 获取版本号 ---
  const { versionInput } = await inquirer.prompt([
    {
      type: 'input',
      name: 'versionInput',
      message: '请输入版本号（留空自动生成）：', // 中文提示
      default: getDefaultVersion(),
    }
  ])
  const VERSION = versionInput.trim() || getDefaultVersion()
  log(`使用的版本号: ${VERSION}`, 'yellow') // 中文日志

  // --- 选择服务 ---
  const availableServices = getAvailableServices()
  if (!availableServices || availableServices.length === 0) {
    log('docker-compose.yaml 中未找到服务。正在退出。', 'red') // 中文日志
    process.exit(1)
  }

  const { services: selectedServices } = await inquirer.prompt([
    {
      type: 'checkbox',
      name: 'services',
      message: '请选择要部署的服务 (空格键选择，回车确认):', // 中文提示
      choices: availableServices,
      validate(answer: string[]) {
        if (answer.length < 1) {
          return '请至少选择一个服务进行部署！' // 中文提示
        }
        return true
      },
    }
  ])
  log(`选中的服务: ${selectedServices.join(', ')}`, 'green') // 中文日志

  // --- 构建、打标签、推送镜像 ---
  log('===== 本地构建并推送流程 =====', 'cyan') // 中文日志
  const localImageTag = `${IMAGE_NAME}:${VERSION}`
  const remoteImageTagVersion = `${REMOTE_REPO}:${VERSION}`
  const remoteImageTagLatest = `${REMOTE_REPO}:latest`

  log(`构建 Docker 镜像: ${localImageTag}`, 'blue') // 中文日志
  // 假设 Dockerfile 在项目根目录 (.)
  // 如果为不同于主机的架构构建，请使用 --platform
  runCommand(`docker buildx build --platform linux/amd64 -t ${localImageTag} . -f ./Dockerfile`) // 指定 Dockerfile (如果需要)

  log(`标记镜像 (版本): ${remoteImageTagVersion}`, 'magenta') // 中文日志
  runCommand(`docker tag ${localImageTag} ${remoteImageTagVersion}`)

  log(`推送版本镜像: ${remoteImageTagVersion}`, 'magenta') // 中文日志
  runCommand(`docker push ${remoteImageTagVersion}`)

  log(`标记镜像 (latest): ${remoteImageTagLatest}`, 'magenta') // 中文日志
  runCommand(`docker tag ${localImageTag} ${remoteImageTagLatest}`)

  log(`推送 latest 镜像: ${remoteImageTagLatest}`, 'magenta') // 中文日志
  runCommand(`docker push ${remoteImageTagLatest}`)

  log('镜像构建并推送完成。', 'green') // 中文日志

  // --- 触发远程部署 ---
  log('===== 远程服务器部署流程 =====', 'cyan') // 中文日志
  // 确保远程路径和脚本名称正确
  // 将选定的服务作为参数传递给远程脚本
  const remoteCommand = `cd ${REMOTE_PROJECT_PATH} && git pull && cd apps/yuhe/docker && ${REMOTE_DEPLOY_SCRIPT} ${selectedServices.join(' ')}`
  const sshCommand = `ssh ${REMOTE_USER_HOST} "${remoteCommand}"`

  log(`执行远程部署命令: ${sshCommand}`, 'yellow') // 中文日志
  runCommand(sshCommand)

  log(`===== YUHE 部署流程完成，版本号: ${VERSION} =====`, 'cyan') // 中文日志

  // 可选：打开相关监控页面
  // execSync('open "你的监控URL"') // 如果需要，请更新为实际 URL
}

// 执行主流程
main().catch((error) => {
  log(`主流程发生未捕获错误: ${error.message}`, 'red') // 中文日志
  process.exit(1)
})