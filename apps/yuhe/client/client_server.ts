import express from 'express'
import axios from 'axios'
import logger from '../../../packages/model/logger/logger'
import { exec } from 'child_process'
import { IReceivedMessage, ISendMessageResult } from '../../../packages/lib/juzi/type'
import { JuziAPI } from '../../../packages/lib/juzi/api'
import { CacheDecorator } from '../../../packages/lib/cache/cache'
import { JuziMessageHandler } from '../../../packages/service/message_handler/juzi/message_handler'
import { SendMessageResultHandler } from '../../../packages/service/message_handler/juzi/send_result_handler'
import { ClientAccountConfig } from '../../../packages/service/database/config'
import { initConfig } from './init'
import { catchGlobalError } from '../../../packages/model/server/server'
import { YuHeEventHandler } from './event_handler'
import { IXingyanPushCallback } from '../../../packages/model/xingyan'
import { ITestEventChangeNextStage, ITestEventClearCache } from './event_type'
import { ChatStateStore } from '../../../packages/service/local_cache/chat_state_store'
import { handleImageMessage, handleUnknownMessage, handleVideoMessage } from './message_handler'

const app = express()
const messageHandler = new JuziMessageHandler({
  handleClassGroupMessage: async () => {},
  handleUnknownMessage,
  handleImageMessage,
  handleVideoMessage
})

app.use(express.json())

app.get('/', (req, res) => {
  logger.log('Hello Client, this is Server!')
  res.send('Hello Client!')
})

app.post('/message', async (req, res) => {
  // 接收消息
  const msg: IReceivedMessage = req.body

  messageHandler.handle(msg) // 添加到消息队列
  res.send('ok')
})

app.post('/event', async (req, res) => {
  // 接收消息
  const data = req.body

  new YuHeEventHandler().handle(data)
  res.send('ok')
})

app.post('/sendResult', async (req, res) => {
  // 接收消息
  const data: ISendMessageResult = req.body

  SendMessageResultHandler.handle(data) // 处理消息发送结果
  res.send('ok')
})

app.post('/yuhe/event', async (req, res) => {
  new YuHeEventHandler().handleYuHeEvent(req.body as IXingyanPushCallback<any>)
  res.send('ok')
})

app.post('/test/event/change_stage', async (req, res) => {
  const data = req.body as ITestEventChangeNextStage

  await ChatStateStore.update(data.chatId, {
    nextStage: data.stage
  })

  res.send({
    code: 200,
    msg: 'ok'
  })
})

app.post('/test/event/clear_cache', async(req, res) => {
  const data = req.body as ITestEventClearCache

  ChatStateStore.clearCache(data.chatId)

  res.send({
    code: 200,
    msg: 'ok'
  })
})

// 缓存 API，提高性能
// JuziAPI.getCustomerInfo = CacheDecorator.decorateAsync(JuziAPI.getCustomerInfo)
JuziAPI.externalIdToWxId = CacheDecorator.decorateAsync(JuziAPI.externalIdToWxId)

catchGlobalError() // 防止 抛错，导致服务停止
initServer()

async function initServer() {
  const name = process.env.WECHAT_NAME

  if (!name) {
    console.error('请设置环境变量 WECHAT_NAME')
    process.exit(1)
  }

  const account = await ClientAccountConfig.getAccountByName(name)
  if (!account) {
    console.error(`找不到${name}对应的账号`)
    process.exit(1)
  }

  await initConfig()

  // 消息处理 Worker
  JuziMessageHandler.startWorker(messageHandler)

  app.listen(account.port, '0.0.0.0', () => {
    console.log(`Server is running on port ${account.port}`)
  })
}


// 测试账号自动化部署
app.post('/webhook', async (req, res) => {
  const payload = req.body
  console.log(JSON.stringify(payload, null, 4))

  // 检查是否为开发分支推送
  if (payload.ref !== 'refs/heads/develop') {
    return res.status(200).json({ message: 'Not moer branch push, ignoring.' })
  }

  // 执行部署脚本
  try {
    // 使用 Promise 封装异步子进程执行
    const runCommand = (command: string): Promise<string> => {
      return new Promise((resolve, reject) => {
        exec(command, (error, stdout, stderr) => {
          if (error) {
            reject(error)
            return
          }
          resolve(stdout)
        })
      })
    }
    // 执行命令

    await runCommand('git pull')
    await runCommand('npm install')
    await runCommand('npx prisma generate')
    try {
      await runCommand('npm run tsc-check')
    } catch (e) {
      // 直接发短信提示报错
      await axios.get(`https://fwalert.com/d675887f-7151-4bb1-9da4-fdb7000c9c23?user=${encodeURIComponent(payload.user.name)}`)
    }
    await runCommand('fuser -k 4097/tcp')

    res.status(200).json({ message: 'Deployment script executed successfully.' })
  } catch (error) {
    logger.error(error)
    res.status(500).json({
      message: 'Deployment failed',
      error: error instanceof Error ? error.message : 'Unknown error'
    })
  }
})