import { Config } from '../../../packages/config'
import { ObjectUtil } from '../../../packages/lib/object'
import logger from '../../../packages/model/logger/logger'

export enum TeacherName {
  ZST = '中神通',
  XW = '小王',
  YJ = '玉姐',
}

export class YuHeConfig extends  Config {
  public static getTeacherName() {
    const teacherName = process.env.TEACHER_NAME

    if (teacherName) {
      if (!(ObjectUtil.enumValues(TeacherName) as string[]).includes(teacherName)) {
        logger.warn(`${teacherName} 应该为 ${ObjectUtil.enumValues(TeacherName).join(',')} 之一`)
        return TeacherName.XW
      }

      return teacherName as TeacherName
    }

    return TeacherName.XW
  }
}