import { GroupNotification } from '../../../../packages/service/group_notification/group_notification'
import { Config } from '../../../../packages/config'
import { loadConfigByWxId } from '../../../../packages/model/bot_config/load_config'

export class YuHeConfigNotify {
  public static async notify(message: string) {
    if (!Config.setting.wechatConfig) {
      Config.setting.wechatConfig = await loadConfigByWxId('1688858335726355')
    }

    await GroupNotification.notify(message, 'R:10933256292171603')
  }
}