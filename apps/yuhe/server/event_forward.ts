import { IXingyanPushCallback, XingyanPushType } from '../../../packages/model/xingyan'
import { ChatDB } from '../../../packages/service/database/chat'
import { PrismaMongoClient } from '../../../packages/model/mongodb/prisma'
import { Retry } from '../../../packages/lib/retry/retry'
import axios from 'axios'
import pLimit from 'p-limit'
import logger from '../../../packages/model/logger/logger'

const limit = pLimit(50)

export class YuHeEventForwardHandler {
  public static async forward(callback: IXingyanPushCallback<any>) {
    switch (callback.type) {
      case XingyanPushType.USER_ENTER_ROOM:
      case XingyanPushType.USER_LEAVE_ROOM:
      case XingyanPushType.PRODUCT_PURCHASE:
      case XingyanPushType.PRODUCT_CLICK:
      case XingyanPushType.ORDER_CLOSE:
        // 只转发这几类事件
        await limit (() => YuHeEventForwardHandler.forwardEvent(callback))
        break

      default:
        logger.log(`暂不支持的回调类型: ${callback.type}`)
    }
  }

  private static async forwardEvent(callback: IXingyanPushCallback<any>) {
    let phone: string = ''
    if (callback.param.mobile) {
      phone = callback.param.mobile
    } else if (callback.param.orderPhone) {
      phone = callback.param.orderPhone
    }

    if (phone) {
      // 查下手机号
      const chat = await ChatDB.getChatByPhone(phone)

      if (!chat) {
        return
      }

      const wx_id = chat?.wx_id
      if (!wx_id) {
        return
      }

      await this.forwardByWxId(wx_id, callback)
    }
  }

  private static async forwardByWxId(wx_id: string, callback: IXingyanPushCallback<any>) {
    // 获取对应的地址进行转发
    const config =  await PrismaMongoClient.getConfigInstance().config.findFirst({
      where: {
        wechatId: wx_id
      }
    })

    if (!config) {
      return
    }

    const serverAddress = config.address

    try {
      await Retry.retry(4, async () => {
        await axios.post(`${serverAddress}/yuhe/event`, callback, { insecureHTTPParser: true })
      }, {
        delayFunc :(retryCount) => {
          if (retryCount === 1) return 2 * 60 * 1000  // 2分钟
          if (retryCount === 2) return 10 * 60 * 1000 // 10分钟
          if (retryCount === 3) return 30 * 60 * 1000 // 30分钟
          return 0  // 之后不再进行重试
        }
      })
    } catch (e) {
      console.error('事件分发失败：', serverAddress, e)
    }
  }
}