import { YuHeWorkflow } from '../workflow/workflow'
import { UUID } from '../../../packages/lib/uuid/uuid'
import { getChatId, getUserId } from '../../../packages/config/chat_id'
import { PrismaClient } from '@prisma/client'
import { PrismaMongoClient } from '../../../packages/model/mongodb/prisma'
import { Situation } from '../../../packages/service/visualized_sop/visualized_sop_type'
import { Queue } from 'bullmq'
import { RedisDB } from '../../../packages/model/redis/redis'
import dayjs from 'dayjs'
import { getVisualizedSopQueueName } from '../../../packages/service/visualized_sop/visualized_sop_task_starter'
import { YuHeEventForwardHandler } from '../server/event_forward'
import { FileHelper } from '../../../packages/lib/file'
import path from 'path'
import { ChatDB } from '../../../packages/service/database/chat'
import { Config } from '../../../packages/config'
import { getState } from '../../../packages/service/llm/state'
import { ChatState } from '../../../packages/service/local_cache/chat_state'
import { ChatStateStore } from '../../../packages/service/local_cache/chat_state_store'

describe('Test', function () {
  jest.setTimeout(10000000)
  beforeAll(() => {

  })

  it('123asdasd', async () => {
    const chat_id = '7881301516129585_1688858213716953'
    // const user_id = getUserId(chat_id)

    await ChatDB.create({
      id: chat_id,
      round_ids: [],
      contact: {
        wx_id: getUserId(chat_id),
        wx_name: '花花', // 这里加好友后去更新
      },
      wx_id: Config.setting.wechatConfig?.id ?? 'local',
      created_at: new Date(),
      chat_state:  {
        nextStage: 'free_talk',
        nodeInvokeCount: {},
        state: {},
        userSlots: {}
      }
    })
  }, 60000)

  it('yuhe import task', async () => {
    console.log(new Date(1746720007588).toLocaleString())

    // yuhe_import_white_list
    const queue = new Queue('yuhe_import_white_list', {
      connection: RedisDB.getInstance()
    })

    // await queue.add(
    //   'dailyImportWhiteList',
    //   { timestamp: Date.now() },
    //   {
    //     repeat: { pattern: '0 0 * * *' }, // 每天 0 点执行
    //     jobId: 'dailyImportWhiteList', // 固定的 jobId 确保只有一个任务
    //   }
    // )

    /**
     * 取最近 20 条 completed / failed，按时间倒序
     */
    const latest = await queue.getJobs(
      ['completed', 'failed'],
      -20,   // start
      -1,    // end
      true   // asc = true => 最后一条就是最新
    )

    console.log(JSON.stringify(latest, null, 4))

    // console.log(JSON.stringify(await queue.getRepeatableJobs(), null, 4))
  }, 60000)

  it('should pass', async () => {
    const userId = UUID.short()
    const chatId = getChatId(userId)

    await YuHeWorkflow.step(chatId, userId, '你是老师？')
  }, 60000)

  it('copy sop', async () => {
    const mongoClient = PrismaMongoClient.getInstance()
    const sops = await mongoClient.sop.findMany()
    for (let i = 0; i < sops.length; i++) {
      const sop = sops[i]
      // if(sop.title.includes('中神通')) {
      //   await mongoClient.sop.delete({where:{id:sop.id}})
      // }
      await mongoClient.sop.create({
        data:{
          ...sop,
          id:undefined,
          title:`中神通_${sop.title}`,
          situations:sop.situations as Situation[]
        }
      })
    }
  })

  it('查看任务', async () => {
    const queueName = getVisualizedSopQueueName('yuhe', '****************')
    const queue = new Queue(queueName, {
      connection: RedisDB.getInstance()
    })
    const jobs = await queue.getJobs('delayed')

    for (const job of jobs) {
      if (job.data.chatId === '7881299551013744_****************') {

        await FileHelper.appendFile(path.join(__dirname, 'job.json'), JSON.stringify(job, null, 4))
      }
    }
  }, 60000)

  it('clear sop', async () => {
    await YuHeEventForwardHandler.forward({ 'param':{ 'account':'visitor_MMgJLF', 'browserType':'PC', 'channelId':****************, 'channelName':'蔡莉', 'headImgUrl':'https://thirdwx.qlogo.cn/mmopen/vi_32/xFZyOhbI1KUn0PAzO9sWPON04flqC83Uk77iaSDiby3If6aZ3JibSrUzlSdzMrHNZIvO5gJoJ3G3eKHIfwa411SFhzcQaTfs85217icJGvjrJmA/132', 'ip':'**************', 'loginTime':'2025-05-07 20:52:13', 'mobile':'', 'nickname':'万友AI链盟&乘浪石', 'openId':'o6-n17MmZ2H2yVWv1vNa0n4FfYDI', 'roomId':********, 'unionId':null, 'userId':****************, 'watchTime':3052 }, 'pushTime':*************, 'roomId':********, 'sign':'438993a529808976b81e78cf2a226790', 'type':8 })

  })

  it('find config', async () => {
    const config =  await PrismaMongoClient.getConfigInstance().config.findFirst({
      where: {
        wechatId: '****************'
      }
    })

    console.log(JSON.stringify(config, null, 4))
  }, 60000)

})