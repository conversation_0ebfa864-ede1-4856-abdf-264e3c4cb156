import { EventTracker, IEventType } from '../../../packages/model/logger/data_driven'
import { ObjectUtil } from '../../../packages/lib/object'
import { HumanTransfer } from '../../../packages/service/human_transfer/human_transfer'
import logger from '../../../packages/model/logger/logger'

export enum YuHeHumanTransferType {
  UnknownMessageType = 1,
  NotBindPhone = 2,
  ProblemSolving = 3,
  FailedToJoinGroup = 4,
  ProcessImage = 5,
  MessageSendFailed = 6,
  HesitatePayment = 8,
  LogOutNotify = 9,
  SoftwareIssue = 12,
  RobotDetected = 13,
  PaidCourse = 14,
  ProcessVideo = 24,
  ProcessVideoFailed = 25
}

/**
 * 宇和项目转人工处理
 */
export class YuHeHumanTransfer {
  /**
   * 转交人工，toBot为true时，表示转交机器人
   * @param chatId
   * @param userId
   * @param transferType
   * @param toHuman
   * @param additionMsg
   */
  public static async transfer(chatId: string, userId: string, transferType: YuHeHumanTransferType, toHuman: boolean | 'onlyNotify' = true, additionMsg?: string) {
    if (userId === 'null') {
      logger.error('[YuHeHumanTransfer] userId is null', transferType)
      return
    }

    if (transferType !== YuHeHumanTransferType.UnknownMessageType)  { // 因为图片，文件等转人工的 日志在上级进行处理，这里不进行重复处理
      EventTracker.track(chatId, IEventType.TransferToManual, { reason: ObjectUtil.enumValueToKey(YuHeHumanTransferType, transferType) })
    }

    // 拼接要发送的消息
    // 通知类型：需要人工接管就写“人工”，反之就写“观察”，不要加“人工”
    const handleType = toHuman === true ? '，请人工处理' : '，请观察'
    const notificationMessages = {
      [YuHeHumanTransferType.UnknownMessageType]: '客户发了一个文件',
      [YuHeHumanTransferType.ProcessImage]: '客户发了一张【图片】',
      [YuHeHumanTransferType.ProcessVideo]: '客户发了一个【视频】',
      [YuHeHumanTransferType.ProblemSolving]: '客户遇到问题',
      [YuHeHumanTransferType.ProcessVideoFailed]: '客户发了一个【视频】，识别失败',
      [YuHeHumanTransferType.NotBindPhone]: '客户手机号绑定失败',
      [YuHeHumanTransferType.SoftwareIssue]: '客户软件或者课程链接出问题',
      [YuHeHumanTransferType.MessageSendFailed]: '消息发送失败',
      [YuHeHumanTransferType.RobotDetected]: '客户识别到了AI',
      [YuHeHumanTransferType.HesitatePayment] : '客户付款犹豫',
      [YuHeHumanTransferType.LogOutNotify]: '客户直播掉线',
      [YuHeHumanTransferType.PaidCourse]: '[烟花]客户已付款[烟花]',
      [YuHeHumanTransferType.FailedToJoinGroup]: '客户拉群失败',
    }

    let message = notificationMessages[transferType] as string + handleType
    if (additionMsg) { message += `\n${additionMsg}` }
    return await HumanTransfer.transfer(chatId, userId, message, toHuman)
  }
}