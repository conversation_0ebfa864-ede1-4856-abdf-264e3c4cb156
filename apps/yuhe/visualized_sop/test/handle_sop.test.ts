import { Queue } from 'bullmq'
import { Config } from '../../../../packages/config'
import { getUserId } from '../../../../packages/config/chat_id'
import { loadConfigByWxId } from '../../../../packages/model/bot_config/load_config'
import { getVisualizedSopQueueName, VisualizedSopTasks } from '../../../../packages/service/visualized_sop/visualized_sop_task_starter'
import { YuheVisualizedSopProcessor } from '../yuhe_visualized_sop_processor'
import { RedisDB } from '../../../../packages/model/redis/redis'
import { ITask } from '../../../../packages/service/visualized_sop/visualized_sop_type'

describe('测试sop', () => {
  test('测试sop', async() => {
    Config.setting.localTest = false
    Config.setting.enterpriseName = 'yuhe'
    Config.setting.wechatConfig = await loadConfigByWxId('1688857404698934')
    const chatId = '7881302146051227_1688857404698934'
    const userId = getUserId(chatId)
    await new YuheVisualizedSopProcessor().handleSopBySopId(chatId, userId, '6805b8946f2370580e61bfef')
  }, 6000000)

  test('观察sop队列', async() => {
    const queue = new Queue<ITask>(getVisualizedSopQueueName('yuhe', '1688858335726355'), {
      connection: RedisDB.getInstance()
    })
    const chatId = '7881302590917957_1688858335726355'
    const jobs = (await queue.getJobs()).filter((item) => item.data.chatId == chatId && item.data.scheduleTime.day == 1)
    const view =  jobs.map((item) =>  ({ delay:item.delay, data:item.data }))
    for (const job of view) {
      console.dir(job)
    }
  })

  test('处理sop', async() => {
    const chatId = '7881303126308651_1688858335726355'
    Config.setting.localTest = false
    Config.setting.wechatConfig = await loadConfigByWxId('1688858335726355')
    Config.setting.enterpriseName = 'yuhe'
    console.log(Config.setting.wechatConfig)
    await new YuheVisualizedSopProcessor().handleSopBySopId(chatId, getUserId(chatId), '680286c3764cd7e26b257461')
  })
})