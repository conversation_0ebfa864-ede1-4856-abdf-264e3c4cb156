import { IWechatConfig } from '../../config/interface'
import { PrismaMongoClient } from '../mongodb/prisma'
import { Config } from '../../config'

interface IMoerEnterpriseConfig {
  notifyGroupId: string
  classGroupId: string
  isGroupOwner?: boolean
  proxyGroupNotify?: boolean
}


export async function loadConfigByWxId(id: string): Promise<IWechatConfig> {
  const config = await PrismaMongoClient.getConfigInstance().config.findFirst(
    {
      where: {
        enterpriseName: Config.setting.enterpriseName,
        wechatId: id
      }
    }
  )
  if (!config) {
    throw new Error('Config not found')
  }
  const enterpriseConfig = config.enterpriseConfig as unknown as IMoerEnterpriseConfig

  const account =  {
    orgToken: config.orgToken,
    nickname: config.accountName,
    wechatId: config.wechatId,
    botUserId: config.botUserId,
    address: config.address,
    port: Number(config.port),
    notifyGroupId: enterpriseConfig.notifyGroupId,
    classGroupId: enterpriseConfig.classGroupId,
    isGroupOwner: enterpriseConfig.isGroupOwner,
    proxyGroupNotify: enterpriseConfig.proxyGroupNotify
  }

  return {
    orgToken: account.orgToken,
    id: account.wechatId,
    name: account.nickname,
    botUserId: account.botUserId,
    notifyGroupId: account.notifyGroupId,
    classGroupId: account.classGroupId,
    isGroupOwner: account.isGroupOwner,
    proxyGroupNotify: account.proxyGroupNotify
  }
}