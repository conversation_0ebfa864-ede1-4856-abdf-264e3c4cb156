import { PrismaMongoClient } from '../mongodb/prisma'
import logger from './logger'


export enum IEventType {
  NodeInvoke = '节点调用',
  TransferToManual = '转交人工',
  ManualReply = '人工回复',
  CourseComplete = '正式课完课',
  CourseArrive = '正式课到课',
  PaymentComplete = '完成支付',
  PaymentNotCompleted = '未完成支付',
  DouyinAnalysisComplete = '完成抖音分析',
  HomeworkComplete = '完成作业',
  thinkStrategy = '思考策略',
}
/**
 * 埋点记录
 */
export class EventTracker {
  public static track(chat_id: string, event: IEventType | string, meta?: Record<string, any>) {
    if (!meta) {
      meta = {}
    }

    // 异步记录
    PrismaMongoClient.getInstance().event_track.create({
      data: {
        timestamp: new Date(),
        chat_id,
        type: event,
        meta
      }
    }).catch((e) => {
      logger.error('埋点失败', e)
    })
  }
}