import axios, { AxiosInstance } from 'axios'
import { Retry } from '../../lib/retry/retry'

interface AuthResponse {
    app_access_token: string
    tenant_access_token: string
    // Add other properties from the response as needed.
}


export interface RecordResponse {
    // Define the structure according to the Feishu API response for records.
    data:  {
        items: Array<{
            fields: {
                [key: string]: any
            }
        }>
        has_more: boolean
    }
    code: number
}

export class FeishuAPI {
  private appId: string
  private appSecret: string
  private httpClient: AxiosInstance

  constructor(appId: string, appSecret: string) {
    this.appId = appId
    this.appSecret = appSecret
    this.httpClient = axios.create({
      baseURL: 'https://open.feishu.cn/open-apis/',
      headers: {
        'Content-Type': 'application/json; charset=utf-8'
      }
    })
  }

  private async getTenantAccessToken(): Promise<string> {
    const body = {
      app_id: this.appId,
      app_secret: this.appSecret
    }

    try {
      const response = await this.httpClient.post<AuthResponse>('auth/v3/app_access_token/internal', body)
      return response.data.tenant_access_token
    } catch (error) {
      console.error('Error fetching tenant access token:', error)
      throw new Error('Failed to obtain tenant access token.')
    }
  }

  public async getDatabaseData(appToken: string, tableId: string): Promise<RecordResponse> {
    try {
      const res =  await Retry.retry(3, async () => {
        const tenantAccessToken = await this.getTenantAccessToken()
        const response =  await this.httpClient.get<RecordResponse>(`bitable/v1/apps/${appToken}/tables/${tableId}/records`, {
          headers: {
            Authorization: `Bearer ${tenantAccessToken}`
          }
        })

        if (response.data && response.data.data.has_more) {
          console.warn('超过500条数据，请使用翻页功能获取所有数据。')
        }

        return response.data
      })
      return res
    } catch (error) {
      console.error('Error fetching database records:', error)
      throw new Error('Failed to fetch database data.')
    }
  }
}
