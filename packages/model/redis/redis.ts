import Redis from 'ioredis'
import { Config } from '../../config'


/**
 * redis开放登录缓存数据库
 */
export class RedisDB extends Redis {

  private static instance?: RedisDB

  private constructor() {
    super({
      host: Config.setting.redis.url,
      password: Config.setting.redis.password,
      lazyConnect: true,
      maxRetriesPerRequest: null
    })
  }

  public static getInstance(): RedisDB {
    if (!this.instance) {
      this.instance = new RedisDB()
      this.instance.on('error', (err) => {
        console.error('Redis Error:', err)
      })
    }

    return this.instance
  }
}
