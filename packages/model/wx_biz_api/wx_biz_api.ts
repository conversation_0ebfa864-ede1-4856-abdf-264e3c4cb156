import { Config } from '../../config/config'
import { HttpHelper } from '../../lib/http/httpHelper'
import { RedisCacheDB } from '../redis/redis_cache'

interface WxBizTokenResponse {
  access_token: string
  expires_in: number
}

interface CustomerAcquisitionIdListResponse {
  errcode:number
  errmsg:string
  link_id_list: string[]
  next_cursor: string
}

export interface LinkInfo {
  link_name: string
  link_id: string
  url: string
}

export class WxBizApi {
  private accessTokenRedisKey = 'moer_wx_biz_access_token'


  public async getCustomerAcquisitionIdList() {
    const url = 'https://qyapi.weixin.qq.com/cgi-bin/externalcontact/customer_acquisition/list_link'
    const accessToken = await this.getAccessToken()
    const params = {
      access_token: accessToken
    }

    const response = await HttpHelper.get<CustomerAcquisitionIdListResponse>(url, undefined, params)

    return response.data
  }

  public async getCustomerAcquisitionInfo(link: string) {
    const accessToken = await this.getAccessToken()
    if (!accessToken) {
      return
    }

    const url = 'https://qyapi.weixin.qq.com/cgi-bin/externalcontact/customer_acquisition/get'

    const params = {
      access_token: accessToken
    }
    const requestBody = {
      link_id:link
    }

    const response = await HttpHelper.post<LinkInfo>(url, requestBody, undefined, params)

    return response.data
  }

  public async createCustomerAcquisitionLink(linkName:string, userId:string) {
    const accessToken = await this.getAccessToken()
    if (!accessToken) {
      return
    }

    const url = 'https://qyapi.weixin.qq.com/cgi-bin/externalcontact/customer_acquisition/create_link'

    const params = {
      access_token: accessToken
    }
    const requestBody = {
      link_name:linkName,
      range:{
        user_list:[
          userId
        ]
      }
    }

    const response = await HttpHelper.post<LinkInfo>(url, requestBody, undefined, params)

    return response.data
  }

  public async deleteCustomerAcquisitionLink(link_id:string) {
    const accessToken = await this.getAccessToken()
    if (!accessToken) {
      return
    }

    const url = 'https://qyapi.weixin.qq.com/cgi-bin/externalcontact/customer_acquisition/delete_link'

    const params = {
      access_token: accessToken
    }
    const requestBody = {
      link_id:link_id
    }

    const response = await HttpHelper.post(url, requestBody, undefined, params)

    return response.data
  }


  private async getAccessToken() {
    const accessToken = await this.getAccessTokenFromRedis()
    if (accessToken)
    {
      return accessToken
    }
    return await this.getAccessTokenFromRequest()
  }

  private async getAccessTokenFromRedis() {
    const accessToken = await new RedisCacheDB(this.accessTokenRedisKey).get()
    if (!accessToken)
    {
      return
    }
    return accessToken
  }


  private async getAccessTokenFromRequest() {
    const url = 'https://qyapi.weixin.qq.com/cgi-bin/gettoken'
    const params = {
      corpid : Config.setting.moerWxBiz.corpid,
      corpsecret : Config.setting.moerWxBiz.corpsecret
    }

    const res = await HttpHelper.get<WxBizTokenResponse>(url, undefined, params)

    if (!res.data || !res.data.access_token)
    {
      return
    }
    // save to redis
    const redis = new RedisCacheDB(this.accessTokenRedisKey)

    await redis.set(res.data.access_token,  res.data.expires_in - 10, 'EX')

    return res.data
  }
}