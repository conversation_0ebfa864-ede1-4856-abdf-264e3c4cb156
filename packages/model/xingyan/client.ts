import axios, { AxiosInstance, AxiosResponse } from 'axios'
import { Config } from '../../config'
import { IGetTokenData, IGetTokenRequest, IXingyanResponse } from './type'
import { Retry } from '../../lib/retry/retry'
import { RedisCacheDB } from '../redis/redis_cache'

/**
 * 星炎云 API 客户端
 */
export class XingyanClient {
  private baseUrl: string
  private httpClient: AxiosInstance
  private tokenCacheKey = 'xingyan_access_token'

  constructor() {
    this.baseUrl = Config.setting.xingyan.baseUrl
    this.httpClient = axios.create({
      baseURL: this.baseUrl,
      headers: {
        'Content-Type': 'application/json'
      }
    })
  }

  /**
   * 获取访问令牌
   * 先从缓存中获取，如果缓存中没有或已过期，则重新获取
   */
  public async getAccessToken(): Promise<string> {
    try {
      // 尝试从 Redis 缓存获取 token
      const cachedToken = await new RedisCacheDB(this.tokenCacheKey).get()
      if (cachedToken) {
        return cachedToken
      }
    } catch (error) {
      console.warn('从缓存获取 token 失败，将重新获取', error)
    }

    // 缓存中没有或获取失败，重新请求 token
    return await this.refreshAccessToken()
  }

  /**
   * 刷新访问令牌
   */
  private async refreshAccessToken(): Promise<string> {
    const params: IGetTokenRequest = {
      cropId: Config.setting.xingyan.cropId,
      secretKey: Config.setting.xingyan.secretKey
    }

    try {
      const response = await this.httpClient.post<IXingyanResponse<IGetTokenData>>('/getAccessToken', params)

      if (response.data.code !== 200) {
        throw new Error(`获取 token 失败: ${response.data.msg}`)
      }

      const tokenData = response.data.data
      const token = tokenData.token

      // 计算过期时间，提前5分钟过期以确保安全
      const expiresAt = new Date(tokenData.expiresIn - 5 * 60 * 1000)
      const ttl = Math.floor((expiresAt.getTime() - Date.now()) / 1000)

      // 缓存 token
      if (ttl > 0) {
        await new RedisCacheDB(this.tokenCacheKey).set(token, ttl)
      }

      return token
    } catch (error) {
      console.error('刷新 token 失败:', error)
      throw new Error(`获取星炎云 token 失败: ${error}`)
    }
  }

  /**
   * 发送 GET 请求
   * @param endpoint API 端点
   * @param params 请求参数
   */
  public async get<T>(endpoint: string, params: any = {}): Promise<AxiosResponse<IXingyanResponse<T>>> {
    const token = await this.getAccessToken()

    try {
      return await Retry.retry(3, async () => {
        return await this.httpClient.get<IXingyanResponse<T>>(endpoint, {
          params,
          headers: {
            'Token': token
          }
        })
      })
    } catch (error: any) {
      if (error.response) {
        console.error(JSON.stringify(error.response.data, null, 2))
      }
      throw new Error(`请求失败 ${endpoint}: ${error}`)
    }
  }

  /**
   * 发送 POST 请求
   * @param endpoint API 端点
   * @param data 请求数据
   */
  public async post<T>(endpoint: string, data: any): Promise<AxiosResponse<IXingyanResponse<T>>> {
    const token = await this.getAccessToken()

    try {
      return await Retry.retry(3, async () => {
        return await this.httpClient.post<IXingyanResponse<T>>(endpoint, data, {
          headers: {
            'Token': token
          }
        })
      }, {
        delayFunc: (count) => count * 200
      })
    } catch (error: any) {
      if (error.response) {
        console.error(error.response.status, JSON.stringify(error.response.data, null, 2))
      }
      throw new Error(`请求失败 ${endpoint}: ${error}`)
    }
  }
}
