
import { OpenAIClient } from '../../lib/ai/llm/client'
import { BingSearch } from './bing'

export class WebSearchRAG {
  public static async searchAndAnswer(query: string): Promise<string> {
    const searchResults = await BingSearch.getSimpleSearchResult(query)

    const references = searchResults.map((result, index) => {
      return `${index + 1}.\n` + `${   result.title  }\n${  result.description}`
    }).join('\n\n')

    const ai = OpenAIClient.getClient()

    const prompt = `You are a helpful and smart education abroad advisor. You are given a user question, and please write clean, concise and accurate answer to the question. You will be given a set of related contexts to the question, each starting with a reference number like x. where x is a number. You must use the references to answer the question.

Your answer must be correct, accurate and written by an expert using an unbiased and professional tone. Please limit to 1024 tokens. Do not give any information that is not related to the question, and do not repeat. Say "information is missing on" followed by the related topic, if the given context do not provide sufficient information.

Your answer must be written in the same language as the question.

Here are the set of contexts:

{context}

Remember, don't blindly repeat the contexts verbatim. Answer in Chinese. And here is the user question:
{question}`.replace('{context}', references).replace('{question}', query)

    console.log(prompt)

    return await ai.predict(prompt)
  }
}