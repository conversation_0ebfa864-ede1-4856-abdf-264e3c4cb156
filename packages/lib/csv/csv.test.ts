import { Csv<PERSON>el<PERSON> } from './csv_parse'
import path from 'path'
import Papa from 'papaparse'


describe('Test', function () {
  beforeAll(() => {

  })

  it('should pass', async () => {
    CsvHelper.write(path.join(__dirname, 'test.csv'), [{ '测试': '你妈', '没了': 'hehe' }])
  }, 60000)

  it('2d json', async () => {
    CsvHelper.write2DJson(path.join(__dirname, 'test.csv'), {
      '小张': {
        '姓名': '小张',
        '身高': '175'
      },
      '小王': {
        '姓名': '小王',
        '身高': '180'
      }
    }, ['1', '2'])
  }, 60000)

  it('1', async () => {
    const jsonData = [
      {
        'A': { 'col1': 'xx', 'col2': 'xx' },
        'B': { 'col1': 'yy', 'col2': 'yy' }
      }
    ]

    // 提取出 JSON 数据的列名（即 key1, key2）
    const columnNames = Object.keys(jsonData[0]['A']) // ["col1", "col2"]

    // 创建 CSV 数据的二维数组
    const csvData: string[][] = []

    // 添加列名行
    csvData.push(['Key', ...columnNames])

    // 逐行添加数据
    Object.keys(jsonData[0]).forEach((key) => {
      const row = [key, ...columnNames.map((col) => jsonData[0][key][col] || '')]
      csvData.push(row)
    })

    // 使用 PapaParse 转换为 CSV 格式
    const csv = Papa.unparse(csvData)

    console.log(csv)
  }, 60000)
})