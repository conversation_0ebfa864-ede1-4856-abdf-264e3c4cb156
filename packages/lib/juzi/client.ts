import axios from 'axios'
import { Retry } from '../retry/retry'
import { Config } from '../../config/config'


interface ApiResponse<T> {
    status: number
    data: T
}

export class JuZiWecomClient {
  private baseUrl: URL
  private readonly token: string

  constructor() {
    this.baseUrl = new URL(Config.setting.juziWecom.baseUrl)
    this.token = Config.setting.juziWecom.token
  }

  async get<T>(requestUri: string, payload: object): Promise<ApiResponse<T>> {
    payload = Object.assign(payload, { token: this.token })

    try {
      const response = await Retry.retry(3, async () => {
        return await axios.get<T>(this.baseUrl.href + requestUri, {
          params: payload,
        })
      })

      return response
    } catch (e: any) {
      if (e.response) {
        console.error(JSON.stringify(e.response.data, null, 2))
      }

      throw new Error(`请求失败${  requestUri  }${e}`)
    }
  }

  async post<T>(requestUri: string, payload: any): Promise<ApiResponse<T>> {
    try {
      const response = await Retry.retry(3, async () => {
        return await axios.post<T>(this.baseUrl.href + requestUri, payload,  {
          params: {
            token: this.token
          }
        })
      }, {
        delayFunc: (count) => count * 200
      })

      return response
    } catch (e: any) {
      if (e.response) {
        console.error(JSON.stringify(e.response.data, null, 2))
      }

      throw new Error(`请求失败${  requestUri  }${e}`)
    }
  }


  async put<T>(requestUri: string, payload: any): Promise<ApiResponse<T>> {

    try {
      const response = await Retry.retry(3, async () => {
        return await axios.put<T>(this.baseUrl.href + requestUri, payload, {
          params: {
            token: this.token
          }
        })
      }, {
        delayFunc: (count) => count * 200
      })

      return { status: response.status, data: response.data }
    } catch (e: any) {
      if (e.response) {
        console.error(JSON.stringify(e.response.data, null, 2))
      }

      throw new Error(`请求失败${  requestUri  }${e}`)
    }
  }
}