import { BilibiliVideoParser } from './parser'
import axios from 'axios'

describe('Test', function () {
  beforeAll(() => {

  })

  it('should pass', async () => {
    const urls = [
      'https://www.bilibili.com/video/av12345',
      'https://www.bilibili.com/video/BV1xQ4y1d72V',
      'http://b23.tv/Mv6WyTh'
    ]


    for (const url of urls) {
      console.log(BilibiliVideoParser.isBilibiliVideoUrl(url))
    }
  })

  it('b23', async () => {
    const url = await BilibiliVideoParser.b23TvToBilibiliUrl('wtf http://b23.tv/Mv6WyTh')
    if (!url) {
      throw new Error('Failed to convert b23.tv url to bilibili url')
    }
    console.log(BilibiliVideoParser.isBilibiliVideoUrl(url))
    console.log(await BilibiliVideoParser.getVideoTitle(url))
  }, 30000)
})