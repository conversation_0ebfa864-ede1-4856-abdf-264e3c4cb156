import { Config } from '../../../config'
import { loadConfigByWxId } from '../../../model/bot_config/load_config'
import { VisualizedSopTasks } from '../visualized_sop_task_starter'

describe('测试清空sop', () => {
  test('测试清空sop', async() => {
    Config.setting.enterpriseName = 'yuhe'
    Config.setting.wechatConfig = await loadConfigByWxId('1688857404698934')
    await VisualizedSopTasks.clearSop('7881302146051227_1688857404698934')
  }, 6000000)
})