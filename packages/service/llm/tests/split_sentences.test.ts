import { LLMReply } from '../llm_reply'

describe('Test', function () {
  beforeAll(() => {

  })

  it('should pass1', async () => {
    const sentences = LLMReply.splitIntoSentences(`收到，是王女士对吧，你的新店做滋补这个品类，年营业额还没跑起来，你对AI数字人也是完全不懂，看到你最关心新店装修怎么开始…
    
    我直接说句真心话，现在开实体店，不结合抖音线上的打法和获客，你就是在闭门造车，尤其滋补类的赛道，客户决策周期长、自己说不明白核心价值，很容易铺得一地鸡毛。装修其实不是最大难题，大部分人是装完了才发现没人进店——流量起不来，一切白搭
    
    还有你对AI和抖音的认知现在属于“空白区”，这反而是机会，因为你没被那些野路子误导过。装修和数字化运营这块，课上都有标准流程还有案例，不是平台硬堆料那套，而是教你怎么一步一步搞定前端引流、空间布置、AI助手嫁接的
    
    关键三点啊，一定要回答我：
    - 抖音，你现在做了吗？有发过内容吗？效果怎样？
    - 你这个店就你自己在做，还是连锁？背后有团队没？
    - 店开在哪里？哪个城市？这直接影响到本地流量打法
    
    你把这三点补全，我给你精确梳理下课上该怎么用好这些工具和玩法`)

    console.log(sentences)


    console.log(await Promise.all(sentences.map((item) => (LLMReply as any).cleanText(item, ''))))
  }, 60000)

  it('2', async () => {
    console.log(LLMReply.splitIntoSentences('你的抖音账号目前存在几个比较明显的问题。首先，头像虽然是个人照，但没有突出你的农产品或个人特色，容易让人记不住。昵称“生活在川K与浙C”也没有体现你卖农产品的身份，客户很难一眼看出你是做什么的。背景图只是蓝色渐变，缺乏吸引力和辨识度。简介“来日并不方长”没有任何钩子，也没告诉客户你能带来什么价值。内容以日常和风景为主，和农产品关联不大，客户很难产生购买欲望。整体来看，账号缺乏清晰定位和吸引力，建议你一定要听中神通老师的直播课，第一课会讲账号定位和人设打造，非常适合你现在的情况！'))
  }, 60000)
  it('3', () => {
    console.log(LLMReply.splitIntoSentencesWithMaxSentences('你的抖音账号目前存在几个比较明显的问题。首先，头像虽然是个人照，但没有突出你的农产品或个人特色，容易让人记不住。昵称“生活在川K与浙C”也没有体现你卖农产品的身份，客户很难一眼看出你是做什么的。背景图只是蓝色渐变，缺乏吸引力和辨识度。简介“来日并不方长”没有任何钩子，也没告诉客户你能带来什么价值。内容以日常和风景为主，和农产品关联不大，客户很难产生购买欲望。整体来看，账号缺乏清晰定位和吸引力，建议你一定要听中神通老师的直播课，第一课会讲账号定位和人设打造，非常适合你现在的情况！', 3))
  })
  it('4', () => {
    console.log(LLMReply.splitIntoSentencesWithMaxSentences(`收到，是王女士对吧，你的新店做滋补这个品类，年营业额还没跑起来，你对AI数字人也是完全不懂，看到你最关心新店装修怎么开始…
    
    我直接说句真心话，现在开实体店，不结合抖音线上的打法和获客，你就是在闭门造车，尤其滋补类的赛道，客户决策周期长、自己说不明白核心价值，很容易铺得一地鸡毛。装修其实不是最大难题，大部分人是装完了才发现没人进店——流量起不来，一切白搭
    
    还有你对AI和抖音的认知现在属于“空白区”，这反而是机会，因为你没被那些野路子误导过。装修和数字化运营这块，课上都有标准流程还有案例，不是平台硬堆料那套，而是教你怎么一步一步搞定前端引流、空间布置、AI助手嫁接的
    
    关键三点啊，一定要回答我：
    - 抖音，你现在做了吗？有发过内容吗？效果怎样？
    - 你这个店就你自己在做，还是连锁？背后有团队没？
    - 店开在哪里？哪个城市？这直接影响到本地流量打法
    
    你把这三点补全，我给你精确梳理下课上该怎么用好这些工具和玩法`, 3))
  })
})