import { HashMessagesHandler } from '../message_handler/interrupt/interrupt_handler'
import { UUID } from '../../lib/uuid/uuid'
import { ChatHistoryService } from '../chat_history/chat_history'

export interface IWorkflowState {
    chat_id: string
    user_id: string
    userMessage: string

    interruptHandler: HashMessagesHandler // 用于辅助打断当前对话，同时只执行一个回复，避免重复
    round_id: string // 用来区分每轮对话的 id
}

export async function getState(chatId: string, userId: string, userMessage = ''): Promise<IWorkflowState> {
  return {
    chat_id: chatId,
    user_id: userId,
    userMessage: userMessage,
    interruptHandler: new HashMessagesHandler(await ChatHistoryService.getChatHistoryByChatId(chatId)),
    round_id: UUID.v4()
  }
}