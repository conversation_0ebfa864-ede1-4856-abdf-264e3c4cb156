import chalk from 'chalk'
import { getChatId } from '../../../config/chat_id'
import { ChatHistoryService } from '../../chat_history/chat_history'
import logger from '../../../model/logger/logger'
import { sleep } from '../../../lib/schedule/schedule'
import { Config } from '../../../config'
import { Chat, ChatDB } from '../../database/chat'
import { getWorkflowByProjectName } from '../../../../apps/registry/registry'
import { GroupNotification } from '../../group_notification/group_notification'

export class MessageReplyService {

  public static async reply(text: string[], senderId: string) {
    const chat_id = getChatId(senderId)
    const chat = await ChatDB.getById(chat_id) as Chat
    let contactName = chat_id
    if (chat && chat.contact && chat.contact.wx_name) { contactName = chat.contact.wx_name }

    try {
      if (!text.join('').trim()) {
        return
      }

      if (!chat) {
        return
      }

      const userMessage = text.join('\n')

      if (Config.setting.onlyReceiveMessage) { // mode
        console.log(chalk.redBright('仅接收消息模式'))
        await ChatHistoryService.addUserMessage(chat_id, userMessage)
        return
      }

      // 添加聊天日志
      if (await ChatDB.isHumanInvolvement(chat_id)) { // 人工参与，则不进行回复
        logger.log(chat_id, '联系人交给人工处理')
        // 存储下客户消息
        await ChatHistoryService.addUserMessage(chat_id, userMessage)
        await GroupNotification.notify(`${contactName} 客户AI未开启，请观察
客户说：${userMessage}`)
        return
      }

      // 下面开始处理消息
      if (userMessage.length >= 100) { // 客户发送的消息比较长的话，mock 一下人工阅读的时间
        await sleep(10 * 1000) // 延缓输出
      }

      const workflow = getWorkflowByProjectName()

      await workflow.step(chat_id, senderId, userMessage)
    } catch (e) {
      logger.error(e)
    }
  }
}