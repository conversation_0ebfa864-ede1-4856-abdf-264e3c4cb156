import { JuziAPI } from '../../../lib/juzi/api'
import { FreeSpiritOss } from '../../../model/oss/oss'
import { QwenMax } from '../../../lib/ai/llm/client'
import axios from 'axios'
import { Config } from '../../../config'
import { Readable } from 'stream'

export class VideoHandler {
  /**
   * Handle video message from Juzi API
   * @param chatId Chat ID
   * @param messageId Message ID
   * @returns Video analysis result from Qwen
   */
  public static async handleVideoMessage(chatId: string, messageId: string): Promise<string> {
    try {
      // 1. Get video URL from Juzi API
      const originalVideoResponse = await JuziAPI.getOriginalImage(chatId, messageId)
      if (originalVideoResponse.code !== 0) {
        throw new Error('Failed to get video URL from Juzi API')
      }
      const videoUrl = originalVideoResponse.data.url

      // 2. Get video stream and upload directly to OSS
      const videoStreamResponse = await axios.get(videoUrl, { responseType: 'stream' })
      const bucket = new FreeSpiritOss('static')
      const ossResult = await bucket.putObjectStream(`videos/${messageId}.mp4`, videoStreamResponse.data as Readable, 'video/mp4')
      const publicUrl = `${Config.setting.oss.static.domain}${ossResult.url}`

      // 3. Analyze video with Qwen
      const qwenClient = QwenMax.getClient()
      const qwenResponse = await qwenClient.invoke([
        {
          role: 'user',
          content: [
            {
              type: 'text',
              text: '请分析这个视频的内容，描述视频中发生了什么，有什么关键信息。'
            },
            {
              type: 'video',
              video_url: publicUrl
            }
          ]
        }
      ])

      return qwenResponse.content as string
    } catch (error) {
      console.error('Error processing video:', error)
      throw error
    }
  }
} 