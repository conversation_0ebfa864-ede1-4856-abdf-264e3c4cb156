import { JuziAPI } from '../../lib/juzi/api'
import { Config } from '../../config'
import { IWecomMsgType } from '../../lib/juzi/type'

/**
 * 群通知
 */
export class GroupNotification {
  public static async notify(message: string, groupId?: string) {
    await JuziAPI.sendGroupMsg(Config.setting.wechatConfig?.id as string, groupId ? groupId : Config.setting.wechatConfig?.notifyGroupId as string, {
      type: IWecomMsgType.Text,
      text: message
    })
  }
}