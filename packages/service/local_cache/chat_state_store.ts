import { ChatState } from './chat_state'
import { ChatDB } from '../database/chat'
import { Config } from '../../config'
import { getUserId } from '../../config/chat_id'
import { IChatState } from './type'

/**
 * 这里的 State 是内存中的状态，每轮对话后，刷新到数据库中
 * 初始化时后，可以从数据库中读取
 */
export class ChatStateStore {
  private static state = new ChatState<IChatState>()

  public static async hasState(chat_id: string) {
    return this.state.get(chat_id) !== undefined
  }

  public static async getFlags<T>(chat_id: string): Promise<T> {
    return (await this.get(chat_id)).state as T
  }

  private static getDefaultState(): IChatState { // 获取初始化的状态
    return  {
      nextStage: 'free_talk',
      nodeInvokeCount: {},
      state: {},
      userSlots: {}
    }
  }

  public static async get(chat_id: string): Promise<IChatState> {
    // 如果缓存中没有数据，尝试从数据库读取
    if (!this.state.has(chat_id)) {
      // 从数据库加载数据
      const dbState = await this.loadFromDatabase(chat_id)

      if (dbState) {
        // 数据库中有数据，使用数据库中的数据
        this.state.set(chat_id, dbState)
        console.log('从数据库加载状态成功', chat_id)
      } else {
        // 数据库中没有数据，创建新的状态
        this.state.set(chat_id, this.getDefaultState())

        // 将新创建的状态保存到数据库
        await this.saveToDatabase(chat_id)
        console.log('创建新状态并保存到数据库', chat_id)
      }
    }

    return this.state.get(chat_id) as IChatState
  }

  // 从数据库加载状态
  private static async loadFromDatabase(chat_id: string): Promise<IChatState | null> {
    try {
      return await ChatDB.getChatStateById(chat_id)
    } catch (error) {
      console.error('从数据库加载状态失败', chat_id, error)
      return null
    }
  }

  // 保存状态到数据库
  private static async saveToDatabase(chat_id: string): Promise<void> {
    try {
      // 这里实现保存到数据库的逻辑
      const state = this.state.get(chat_id)
      if (state) {
        // 如果 Chat 不存在，先新建 Chat
        if (await ChatDB.getById(chat_id) === null) {
          await ChatDB.create({
            id: chat_id,
            round_ids: [],
            contact: {
              wx_id: getUserId(chat_id),
              wx_name: '', // 这里加好友后去更新
            },
            wx_id: Config.setting.wechatConfig?.id ?? 'local',
            created_at: new Date(),
            chat_state: state
          })
        } else {
          await ChatDB.updateState(chat_id, state)
        }
      }

    } catch (error) {
      console.error('保存状态到数据库失败', chat_id, error)
    }
  }

  // 更新状态（当状态发生变化时调用）
  public static async update(chat_id: string, updates: Partial<IChatState>): Promise<void> {
    const currentState = await this.get(chat_id)
    const updatedState = this.deepMerge(currentState, updates)

    // 更新内存缓存
    this.state.set(chat_id, updatedState)

    // 同时更新数据库
    await this.saveToDatabase(chat_id)
  }

  public static clearCache(chat_id: string) {
    this.state.delete(chat_id)
  }

  public static async clear(chat_id: string) {
    this.state.delete(chat_id)

    // 数据库清空状态
    await ChatDB.updateState(chat_id, this.getDefaultState())
  }

  /**
   * 注意 name 传递的是类名
   * @param chat_id
   * @param name
   */
  public static async getNodeCount(chat_id: string, name: string) {
    const state = await this.get(chat_id)
    if (!state.nodeInvokeCount[name]) {
      state.nodeInvokeCount[name] = 0
    }
    return state.nodeInvokeCount[name]
  }

  public static async increaseNodeCount(chat_id: string, name: string) {
    const state = await this.get(chat_id)
    // 初始化节点计数为0（如果还不存在）
    if (!state.nodeInvokeCount[name]) {
      state.nodeInvokeCount[name] = 0
    }

    // 增加调用次数
    state.nodeInvokeCount[name] += 1
    // 保存更新后的 state
    this.state.set(chat_id, state)
  }

  private static deepMerge (target: any, source: any): any {
    const output = { ...target }
    if (Array.isArray(target) && Array.isArray(source)) {
      return source
    } else if (typeof target === 'object' && typeof source === 'object') {
      for (const key in source) {
        if (source [key] instanceof Object && target instanceof Object && key in target) {
          output [key] = this.deepMerge (target [key], source [key])
        } else {
          output [key] = source [key]
        }
      }
    }
    return output
  }

}

