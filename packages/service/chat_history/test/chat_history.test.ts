import { ChatHistoryService } from '../chat_history'

describe('Test', function () {
  beforeAll(() => {})

  it('test isRepeatedMsg', async() => {
    const chat_id = '7881302298050442_1688858335726355'
    const line = 'http://QwsKvkDN.w.mrstyy.com/r/AgKUPh0h\n\n记得一定要补课哈，今晚直播内容会更贴合你实体店的实际问题哦，别错过哦！'
    const isRepeated = await ChatHistoryService.isRepeatedMsg(chat_id, line)
    console.log(isRepeated)
  }, 600000)
})