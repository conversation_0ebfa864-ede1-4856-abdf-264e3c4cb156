{"name": "eliza", "private": true, "scripts": {"tsc-check": "tsc --noEmit", "check-import": "ts-node scripts/check_dependecies.ts", "yuhe:test": "export NODE_ENV=dev WECHAT_NAME=yuhe_test && ts-node apps/yuhe/client/client_server.ts", "yuhe:online-test": "export NODE_ENV=dev WECHAT_NAME=yuhe_online_test && ts-node apps/yuhe/client/client_server.ts", "circular-check": "madge --circular --extensions ts apps packages", "client": "ts-node apps/yuhe/client/client_server.ts", "yuhe:event:server": "ts-node apps/yuhe/server/event_server.ts", "yuhe:deploy": "ts-node apps/yuhe/docker/deploy.ts"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/ali-oss": "^6.0.8", "@types/express": "^4.17.19", "@types/jest": "^29.5.12", "@types/jest-when": "^3.5.5", "@types/mime": "^3.0.3", "@types/node": "^20.7.0", "@types/papaparse": "^5.3.15", "@types/qrcode-terminal": "^0.12.0", "@typescript-eslint/eslint-plugin": "^8.15.0", "@typescript-eslint/parser": "^8.15.0", "csv-parse": "^4.16.3", "esbuild": "^0.19.4", "eslint": "^9.25.0", "eslint-config-prettier": "^10.1.0", "fuzzy": "^0.1.3", "globals": "^16.0.0", "globby": "^11.0.4", "husky": "^7.0.4", "inquirer": "^8.2.6", "jest": "^29.7.0", "jest-when": "^3.7.0", "js-yaml": "^4.1.0", "langsmith": "^0.2.15", "madge": "^8.0.0", "mockdate": "^3.0.5", "nodemon": "^3.0.1", "papaparse": "^5.4.1", "prettier": "^3.5.3", "prisma": "^6.9.0", "readline-sync": "^1.4.10", "ts-jest": "^29.2.4", "ts-node": "^10.9.1", "typescript": "5.8.2", "typescript-eslint": "^8.30.1", "yaml": "^1.10.2", "zip-local": "^0.3.5"}, "dependencies": {"@alicloud/pop-core": "^1.8.0", "@elastic/elasticsearch": "^8.13.1", "@faker-js/faker": "^8.4.1", "@langchain/community": "^0.3.40", "@langchain/core": "^0.3.44", "@langchain/openai": "^0.5.5", "@prisma/client": "6.6.0", "@types/express-ws": "^3.0.4", "@types/js-yaml": "^4.0.9", "ali-oss": "^6.20.0", "alibabacloud-nls": "^1.0.2", "async-sema": "^3.1.1", "axios": "^1.5.1", "bullmq": "^5.12.9", "chalk": "^4.1.2", "cheerio": "^1.0.0-rc.12", "cron": "^3.1.7", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "express": "^4.18.2", "express-ws": "^5.0.2", "fast-xml-parser": "^4.3.5", "ioredis": "^5.4.1", "jsonrepair": "^3.6.0", "langchain": "^0.3.21", "lru-cache": "^10.2.0", "markdown-to-text": "^0.1.1", "mime": "^3.0.0", "multer": "^1.4.5-lts.1", "openai": "^4.98.0", "openai-zod-functions": "^0.1.2", "p-limit": "3.1.0", "pickleparser": "^0.2.1", "pino": "^8.17.2", "pino-pretty": "^10.3.1", "qrcode-terminal": "^0.12.0", "redlock": "^5.0.0-beta.2", "set-interval-async": "^3.0.3", "short-uuid": "^4.2.2", "silk-wasm": "^3.6.1", "string-width": "4.2.3", "xbb-api": "^0.0.4", "zod": "^3.23.8"}, "packageManager": "pnpm@9.0.0", "engines": {"node": ">=18"}}