import { SopTopicShow } from '@/app/component/sop/sopTopicShow'
import { queryAllTags } from '@/app/yuhe/api/sop_tag'
import { changeSopTopicEnable, copyTopicToTag, deleteTopic, querySopTopicByTag, renameTopic } from '@/app/yuhe/api/sop_topic'

export default async function Page({ params }:{
  params: Promise<{ tag: string }>
}) {
  const { tag } = await params
  const decodeTag = decodeURIComponent(tag)
  return <div>
    <SopTopicShow
      tag={decodeTag}
      querySopTopicByTag={querySopTopicByTag}
      changeSopTopicEnable={changeSopTopicEnable}
      copyTopicToTag={copyTopicToTag}
      deleteTopic={deleteTopic}
      renameTopic={renameTopic}
      queryAllTags={queryAllTags}
    />
  </div>
}