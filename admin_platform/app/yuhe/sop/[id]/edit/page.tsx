'use client'
import { use } from 'react'
import { getConditionJudgeKeys, getCustomKeys, getVariableMapKeys, getLinkSourceVariableTagKeys, querySopById, updateSop } from '@/app/yuhe/api/sop'
import { EditSop } from '@/app/component/sop/editSop'

export default function Page({ params }: { params: Promise<{ id: string }> }) {
  const param = use(params)
  return <EditSop id={param.id}
    getConditionJudgeKeys={getConditionJudgeKeys}
    getCustomKeys={getCustomKeys}
    getLinkSourceVariableTagKeys={getLinkSourceVariableTagKeys}
    getVariableMapKeys={getVariableMapKeys}
    querySopById={querySopById}
    updateSop={updateSop}
  />
}
