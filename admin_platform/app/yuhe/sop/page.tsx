import { queryAccounts } from '../api/account'
import { SopTags } from '@/app/component/sop/sopTags'
import { changeTagEnable, deleteTag, queryTags, updateTagEnableAccount } from '../api/sop_tag'
import { updateMq, updateRedisSop, validateSops } from '../api/sop'

export default async function Page({
  searchParams,
}: {
  searchParams: Promise<{
    page?: string;
    pageSize?: string ;
  }>;
}) {
  const searchParam = await searchParams
  const page = Number(searchParam.page ?? 1)
  const pageSize = Number(searchParam.pageSize ?? 20)
  const tags = await queryTags({ page, pageSize })
  const accounts = await queryAccounts()
  return <SopTags
    tags={tags}
    accounts={accounts}
    tagLinkPrefix='./sop/tag/'
    validateSops={validateSops}
    updateMq={updateMq}
    updateRedisSop={updateRedisSop}
    updateTagEnableAccount={updateTagEnableAccount}
    changeTagEnable={changeTagEnable}
    deleteTag={deleteTag}
  />
}
