'use server'

import { AccountD<PERSON>, GroupTeacher } from '@/app/type/account'
import { PrismaMongoClient } from '../../../../packages/model/mongodb/prisma'

export async function queryAccounts():Promise<AccountData[]> {
  const mongoClient = PrismaMongoClient.getConfigInstance()
  const result = await mongoClient.config.findMany({ where:{ enterpriseName:'yuhe' }, select:{ id:true, accountName:true, wechatId:true } })
  return result
}

export async function queryGroupTeacherData():Promise<GroupTeacher[]> {
  const mongoClient = PrismaMongoClient.getInstance()
  const result = await mongoClient.group_teacher.findMany()
  return result
}