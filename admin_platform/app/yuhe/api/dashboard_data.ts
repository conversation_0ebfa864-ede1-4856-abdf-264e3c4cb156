'use server'

import { DashboardData, DashboardDataWithChatHistory, SimpleChatHistory } from '@/app/type/dashboard_data'
import { PrismaMongoClient } from '../../../../packages/model/mongodb/prisma'

export async function createManyDashboardData(dashboardData:Omit<DashboardData, 'id' | 'created_at'>[]):Promise<void> {
  const mongoClient = PrismaMongoClient.getInstance()
  await mongoClient.dashboard_data.createMany({ data:dashboardData })
}

export async function queryDashboardData(page:number, pageSize:number): Promise<DashboardDataWithChatHistory[]> {
  const mongoClient = PrismaMongoClient.getInstance()
  const dashboard = await mongoClient.dashboard_data.findMany({ orderBy:{ created_at:'desc' }, take:pageSize, skip:(page - 1) * pageSize })
  return await mergeDashboardDataWithChatHistory(dashboard)
}

export async function queryDashboardDataByChatId(chatId:string): Promise<DashboardDataWithChatHistory[]> {
  const mongoClient = PrismaMongoClient.getInstance()
  const dashboard = await mongoClient.dashboard_data.findMany({ where:{ chat_id:chatId } })
  return await mergeDashboardDataWithChatHistory(dashboard)
}

export async function mergeDashboardDataWithChatHistory(dashboard:DashboardData[]):Promise<DashboardDataWithChatHistory[]> {
  const mongoClient = PrismaMongoClient.getInstance()
  const chatHistoryIdSet:Set<string> = new Set<string>()
  for (const item of dashboard) {
    for (const id of item.chat_history_id) {
      chatHistoryIdSet.add(id)
    }
  }
  const chatHistory = await mongoClient.chat_history.findMany({ where:{ id:{ in:[...chatHistoryIdSet] } }, select:{ id:true, chat_id:true, content:true, created_at:true, role:true } })
  const chatHistoryMap = new Map<string, SimpleChatHistory>()
  for (const item of chatHistory) {
    chatHistoryMap.set(item.id, item)
  }
  return dashboard.map((item) => {
    return {
      ...item,
      chat_history:item.chat_history_id.map((singleChatHistory) => {
        return chatHistoryMap.get(singleChatHistory)
      }).filter((singleChatHistory) => singleChatHistory != undefined).sort((a, b) => a.created_at.getTime() - b.created_at.getTime())
    }
  })
}

export async function queryDashboardDataCount(): Promise<number> {
  const mongoClient = PrismaMongoClient.getInstance()
  return await mongoClient.dashboard_data.count()
}

export async function deleteDashboardData(id:string): Promise<void> {
  const mongoClient = PrismaMongoClient.getInstance()
  await mongoClient.dashboard_data.delete({ where:{ id } })
}

export async function deleteDashboardTag(id:string, tag:string): Promise<void> {
  const mongoClient = PrismaMongoClient.getInstance()
  const info = await mongoClient.dashboard_data.findFirst({ where:{ id } })
  if (!info?.tag) {
    return
  }
  const newTag = info.tag.filter((item) => item != tag)
  await mongoClient.dashboard_data.update({ where:{ id }, data:{ tag:newTag } })
}

export async function addDashboardTag(id:string, tag:string): Promise<void> {
  const mongoClient = PrismaMongoClient.getInstance()
  const info = await mongoClient.dashboard_data.findFirst({ where:{ id } })
  if (!info) {
    return
  }
  // Check if tag already exists
  if (info.tag.includes(tag)) {
    return
  }
  const newTag = [...info.tag, tag]
  await mongoClient.dashboard_data.update({ where:{ id }, data:{ tag:newTag } })
}

export async function updateDashboardDescription(id:string, description:string): Promise<void> {
  const mongoClient = PrismaMongoClient.getInstance()
  await mongoClient.dashboard_data.update({ where:{ id }, data:{ description } })
}