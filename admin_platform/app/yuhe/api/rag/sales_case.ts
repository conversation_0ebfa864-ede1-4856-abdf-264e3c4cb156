'use server'
import { v4 } from 'uuid'
import ElasticSearchService, { IElasticEmbeddingRes } from '../../../../../packages/model/elastic_search/elastic_search'
import { FreeSpiritOss } from '../../../../../packages/model/oss/oss'
import { RAGHelper } from '../../../../../packages/service/rag/rag'
import { Document } from 'langchain/document'
import { AliyunCredentials } from '../../../../../packages/lib/cer'

const salesCaseRagIndex = 'yuhe_sales_case'

const ossPrefix = 'https://free-spirit-static.oss-cn-hangzhou.aliyuncs.com'

AliyunCredentials.initialize({
  region: 'cn-hangzhou',
  accountId: '****************',
  accessKeyId: 'LTAI5tRVPxefUtgyLCfc5f69',
  secretAccessKey: '******************************',
})

export async function insertSalesCase({ industry, description, caseClass, formData }:{
  industry: string,
  description: string,
  caseClass: string,
  formData: FormData
}) {
  const images:string[] = []
  const files = formData.getAll('file') as File[]
  const bucket = new FreeSpiritOss('static')
  for (const file of files) {
    const splitedName = file.name.split('.')
    if (splitedName.length < 2) {
      throw ('名字错误')
    }
    const name = `/yuhe/sales_case/${splitedName.slice(0, -1)}_${v4()}.${splitedName[splitedName.length - 1]}`
    images.push(`${ossPrefix}${name}`)
    const readableBuffer = Buffer.from(await file.arrayBuffer())
    await bucket.putObject(`${name}`, readableBuffer)
  }
  await RAGHelper.addDocuments(salesCaseRagIndex, [
    new Document({
      pageContent: industry,
      metadata: {
        topic:industry,
        description:description,
        doc: caseClass,
        images: JSON.stringify(images)
      },
    }),
  ])
}

export async function searchRagCase({ industry, number, minScore }: {
    industry: string
    number: number
    minScore: number
  }): Promise<IElasticEmbeddingRes[]> {
  return await ElasticSearchService.embeddingSearch(
    salesCaseRagIndex,
    industry,
    number,
    minScore,
  )
}

export async function updateSalesCase({
  industry,
  description,
  caseClass,
  images,
  id
}:{
  industry: string
  description: string
  caseClass: string,
  images:string[]
  id: string
}):Promise<void> {
  const document = new Document({
    metadata: {
      topic:industry,
      description:description,
      doc: caseClass,
      images: JSON.stringify(images)
    },
    pageContent: industry,
  })

  await RAGHelper.addDocumentsWithIds(salesCaseRagIndex, [document], [id])
}

export async function deleteSalesCase(docId: string, images: string[]) {
  //删除图片
  if (images.length > 0) {
    const bucket = new FreeSpiritOss('static')
    await bucket.deleteObjects(images.map((name) => name.replaceAll(ossPrefix, '')))
  }
  await ElasticSearchService.deleteDocuments(salesCaseRagIndex, [docId])
}