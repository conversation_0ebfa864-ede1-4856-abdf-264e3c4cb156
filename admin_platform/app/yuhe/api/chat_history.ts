'use server'
import { ChatHistory } from '@/app/type/chat_history'
import { PrismaMongoClient } from '../../../../packages/model/mongodb/prisma'

export async function queryChatHistoryByChatId(chatId:string):Promise<ChatHistory[]> {
  const mongoClient = PrismaMongoClient.getInstance()
  const history = await mongoClient.chat_history.findMany({ where:{ chat_id:chatId }, orderBy:[{ created_at:'asc' }] })
  return history as unknown as ChatHistory[]
}