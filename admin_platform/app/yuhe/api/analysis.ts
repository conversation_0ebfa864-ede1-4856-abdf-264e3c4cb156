'use server'

import { PrismaMongoClient } from '../../../../packages/model/mongodb/prisma'
import { IChattingFlag } from '../../../../apps/yuhe/state/user_flags'
import { AnalysisData } from '@/app/type/analysis'
import { contentWithFrequency } from '../../../../packages/service/local_cache/type'
import { UserSlots } from '../../../../packages/service/user_slots/extract_user_slots'
import { isFillAnyUserSlots, isFillUserSlots } from '../../../../apps/yuhe/visualized_sop/visualized_sop_variable'

export async function getAnalysisData(startCourseNo:number, endCourseNo:number):Promise<AnalysisData[]> {
  const mongoClient = PrismaMongoClient.getInstance()
  const getChatInfo = async(startCourseNo:number, endCourseNo:number) => {
    return await mongoClient.chat.findMany({ where:{ course_no:{ gte:startCourseNo, lte:endCourseNo } }, select:{ id:true, wx_id:true, contact:true, chat_state:true, course_no:true, course_no_ori:true, phone:true, ip:true } })
  }
  const getAccounts = async() => {
    const mongoConfigClient = PrismaMongoClient.getConfigInstance()
    return await mongoConfigClient.config.findMany({ where:{ enterpriseName:'yuhe' }, select:{ accountName:true, wechatId:true } })
  }

  const [chatInfo, accounts] = await Promise.all([getChatInfo(startCourseNo, endCourseNo), getAccounts()])
  const accountMap = new Map<string, string>()
  for (const account of accounts) {
    accountMap.set(account.wechatId, account.accountName)
  }
  const filtedChatInfo = chatInfo.filter((item) => item.wx_id != '****************')

  const chatHistory = await mongoClient.chat_history.findMany({ where:{ chat_id:{ in:chatInfo.map((item) => item.id) }, role:'user' }, select:{ chat_id:true, id:true } })
  const chatEventTrack = await mongoClient.event_track.findMany({ where:{ chat_id:{ in:chatInfo.map((item) => item.id) }, type:'完成支付' } })
  const paidTimeMap = new Map<string, Date>()
  for (const event of chatEventTrack) {
    paidTimeMap.set(event.chat_id, event.timestamp)
  }

  return filtedChatInfo.filter((chat) => !['****************', '****************', '****************', '****************', '****************'].includes(chat.contact.wx_id)).map((chat) => {
    const state = chat.chat_state.state as IChattingFlag
    const userSlots = UserSlots.fromRecord(chat.chat_state.userSlots as Record<string, contentWithFrequency>)
    return {
      chatId:chat.id,
      name: chat.contact.wx_name,
      phone: chat.phone ?? '',
      ip:chat.ip ?? '',
      paidTime:paidTimeMap.get(chat.id) ?? null,
      isInviteGroupFailAfterPayment:state.is_invite_group_fail_after_payment ?? false,
      assistant: accountMap.get(chat.wx_id) ?? '未知',
      chatNumber: chatHistory.filter((item) => item.chat_id == chat.id).length,
      isPaid:state.is_complete_payment ?? false,
      courseNo: chat.course_no ?? 0,
      courseNoOri:chat.course_no_ori ?? 0,
      isFillAnyUserSlots:isFillAnyUserSlots(userSlots),
      isFillUserSlots:isFillUserSlots(userSlots),
      isCompleteDouyinAnalysis:state.is_complete_douyin_analysis ?? false,
      isCompleteHomework1: state.is_complete_homework1 ?? false,
      isCompleteHomework2: state.is_complete_homework2 ?? false,
      isCompleteHomework3: state.is_complete_homework3 ?? false,
      isAttendCourseDay1: state.is_attend_course_day1 ?? false,
      isAttendCourseDay2: state.is_attend_course_day2 ?? false,
      isAttendCourseDay3: state.is_attend_course_day3 ?? false,
      isAttendCourseDay4: state.is_attend_course_day4 ?? false,
      isCompleteCourseDay1: state.is_complete_course_day1 ?? false,
      isCompleteCourseDay2: state.is_complete_course_day2 ?? false,
      isCompleteCourseDay3: state.is_complete_course_day3 ?? false,
      isCompleteCourseDay4: state.is_complete_course_day4 ?? false,
    }
  })
}