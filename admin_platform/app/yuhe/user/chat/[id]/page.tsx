'use client'
import { use } from 'react'
import { queryChatHistoryByChatId } from '../../../api/chat_history'
import { queryLogByChatId } from '../../../api/log_store'
import { ChatHistory } from '@/app/component/user/chat_history'
import { createManyDashboardData, queryDashboardDataByChatId } from '@/app/yuhe/api/dashboard_data'
export default function Page({ params }: { params: Promise<{ id: string }> }) {
  const { id } = use(params)
  return <ChatHistory queryDashboardDataByChatId={queryDashboardDataByChatId} createManyDashboardData={createManyDashboardData} id={id} queryChatHistoryByChatId={queryChatHistoryByChatId} queryLogByChatId={queryLogByChatId}langsmithProjectId='06e01395-e9a2-41e9-8fea-ba3fed9aa3f0'/>
}