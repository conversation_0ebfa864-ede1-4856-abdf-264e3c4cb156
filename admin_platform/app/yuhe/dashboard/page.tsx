import { Dashboard } from '@/app/component/dashboard'
import { deleteDashboardData, deleteDashboardTag, addDashboardTag, updateDashboardDescription, queryDashboardData, queryDashboardDataCount } from '../api/dashboard_data'

export default async function Page({ searchParams }:{
  searchParams: Promise<{ page?:string, pageSize?:string }>
}) {
  const searchParam = await searchParams
  return <Dashboard
    page={Number(searchParam.page ?? 1)}
    pageSize={Number(searchParam.pageSize ?? 20)}
    queryDashboardData={queryDashboardData}
    queryDashboardDataCount={queryDashboardDataCount}
    deleteDashboardData={deleteDashboardData}
    deleteDashboardTag={deleteDashboardTag}
    addDashboardTag={addDashboardTag}
    updateDashboardDescription={updateDashboardDescription}
  />
}