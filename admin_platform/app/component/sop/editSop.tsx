'use client'

import { useRouter } from 'next/navigation'
import { useState, useEffect } from 'react'
import { toast } from 'react-toastify'
import { SopEdit } from './sopEdit'
import { Situation } from '../../../../packages/service/visualized_sop/visualized_sop_type'
import { Sop } from '@/app/type/sop'

export function EditSop({
  id,
  getConditionJudgeKeys,
  getCustomKeys,
  getLinkSourceVariableTagKeys,
  getVariableMapKeys,
  querySopById,
  updateSop
}:{
    id:string
    getConditionJudgeKeys(): Promise<string[]>
    getCustomKeys(): Promise<string[]>
    getVariableMapKeys(): Promise<string[]>
    getLinkSourceVariableTagKeys(): Promise<string[]>
    querySopById(id: string): Promise<Sop>
    updateSop(sop_id: string, sop: Partial<Sop>): Promise<void>
}) {
  const router = useRouter()
  const [situations, setSituations] = useState<Situation[]>([
    {
      conditions: [],
      action: [],
    },
  ])
  const [conditionJudgeKeys, setConditionJudgeKeys] = useState<string[]>([])
  const [customKeys, setCustomKeys] = useState<string[]>([])
  const [variableKeys, setVariableKeys] = useState<string[]>([])
  const [linkSourceVariableTagKeys, setLinkSourceVariableTagKeys] = useState<
    string[]
  >([])
  const [title, setTitle] = useState<string>('')
  const [week, setWeek] = useState<number>(0)
  const [day, setDay] = useState<number>(1)
  const [time, setTime] = useState<string>('')
  const [tag, setTag] = useState<string>('')
  const [topic, setTopic] = useState<string>('')
  const [enable, setEnable] = useState<boolean>(false)
  useEffect(() => {
    getConditionJudgeKeys().then((keys) => {
      setConditionJudgeKeys(keys)
    })
    getCustomKeys().then((keys) => {
      setCustomKeys(keys)
    })
    getVariableMapKeys().then((keys) => {
      setVariableKeys(keys)
    })
    getLinkSourceVariableTagKeys().then((keys) => {
      setLinkSourceVariableTagKeys(keys)
    })
  }, [])
  useEffect(() => {
    toast
      .promise(querySopById(id), {
        pending: 'query pending',
        success: 'query success',
        error: 'query error',
      })
      .then((resultSop) => {
        if (resultSop) {
          setTitle(resultSop.title)
          setWeek(resultSop.week)
          setDay(resultSop.day)
          setTime(resultSop.time)
          setSituations(resultSop.situations as Situation[])
          setTag(resultSop.tag)
          setEnable(resultSop.enable)
          setTopic(resultSop.topic)
        } else {
          toast.error('sop is null')
        }
      })
  }, [id])
  return <div className="m-2">
    <SopEdit
      situations={situations}
      setSituations={setSituations}
      conditionJudgeKeys={conditionJudgeKeys}
      customKeys={customKeys}
      variableKeys={variableKeys}
      linkSourceVariableTagKeys={linkSourceVariableTagKeys}
      title={title}
      setTitle={setTitle}
      week={week}
      setWeek={setWeek}
      day={day}
      setDay={setDay}
      time={time}
      setTime={setTime}
      reset={false}
      tag={tag}
      topic={topic}
      saveSop={async (sop) => {
        await updateSop(id, { ...sop, enable })
        router.push('.')
      }}
      hint={{
        success: 'update success',
        pending: 'update pending',
        error: 'update error',
      }}
    />
  </div>

}