'use client'

import { useState } from 'react'
import { DashboardTag } from '@/app/type/dashboard_tag'
import { RxCross2 } from 'react-icons/rx'
import { FaFilter, FaTimes } from 'react-icons/fa'

interface TagFilterProps {
  allTags: DashboardTag[]
  selectedTagIds: string[]
  onTagToggle: (tagId: string) => void
  onClearAll: () => void
}

export function TagFilter({ allTags, selectedTagIds, onTagToggle, onClearAll }: TagFilterProps) {
  const [showAllTags, setShowAllTags] = useState(false)

  const selectedTags = allTags.filter(tag => selectedTagIds.includes(tag.id))
  const unselectedTags = allTags.filter(tag => !selectedTagIds.includes(tag.id))

  return (
    <div className="mb-4 p-4 bg-gray-50 rounded-lg">
      <div className="flex items-center gap-2 mb-3">
        <FaFilter className="w-4 h-4 text-gray-600" />
        <span className="font-medium text-gray-700">标签筛选</span>
        {selectedTagIds.length > 0 && (
          <button
            onClick={onClearAll}
            className="btn btn-xs btn-error ml-auto"
            title="清除所有筛选"
          >
            <FaTimes className="w-3 h-3" />
            清除筛选
          </button>
        )}
      </div>

      {/* 已选择的标签 */}
      {selectedTags.length > 0 && (
        <div className="mb-3">
          <div className="text-sm text-gray-600 mb-2">已选择的标签:</div>
          <div className="flex flex-wrap gap-2">
            {selectedTags.map((tag) => (
              <div
                key={tag.id}
                className="badge badge-primary flex items-center gap-1 cursor-pointer hover:badge-primary-focus"
                onClick={() => onTagToggle(tag.id)}
              >
                <div 
                  className="w-2 h-2 rounded-full bg-white opacity-80" 
                  style={{ backgroundColor: tag.color || '#3B82F6' }}
                ></div>
                {tag.name}
                <RxCross2 className="w-3 h-3" />
              </div>
            ))}
          </div>
        </div>
      )}

      {/* 可选择的标签 */}
      {unselectedTags.length > 0 && (
        <div>
          <div className="flex items-center gap-2 mb-2">
            <div className="text-sm text-gray-600">可选择的标签:</div>
            {unselectedTags.length > 10 && (
              <button
                onClick={() => setShowAllTags(!showAllTags)}
                className="btn btn-xs btn-ghost"
              >
                {showAllTags ? '收起' : `显示全部 (${unselectedTags.length})`}
              </button>
            )}
          </div>
          <div className="flex flex-wrap gap-2">
            {(showAllTags ? unselectedTags : unselectedTags.slice(0, 10)).map((tag) => (
              <div
                key={tag.id}
                className="badge badge-outline cursor-pointer hover:badge-primary hover:text-white transition-colors"
                onClick={() => onTagToggle(tag.id)}
              >
                <div 
                  className="w-2 h-2 rounded-full mr-1" 
                  style={{ backgroundColor: tag.color || '#3B82F6' }}
                ></div>
                {tag.name}
              </div>
            ))}
          </div>
        </div>
      )}

      {allTags.length === 0 && (
        <div className="text-sm text-gray-500 text-center py-4">
          暂无标签
        </div>
      )}
    </div>
  )
}
