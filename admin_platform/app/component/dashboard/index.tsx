'use server'

import { DashboardDataWithChatHistory } from '@/app/type/dashboard_data'
import { DashboardShow } from './dashboardShow'

export async function Dashboard({
  page,
  pageSize,
  queryDashboardData,
  queryDashboardDataCount,
  deleteDashboardData,
  deleteDashboardTag,
  addDashboardTag,
  updateDashboardDescription
}:{
  page: number,
  pageSize:number,
  queryDashboardData(page:number, pageSize:number):Promise<DashboardDataWithChatHistory[]>
  queryDashboardDataCount(): Promise<number>
  deleteDashboardData(id:string): Promise<void>
  deleteDashboardTag(id:string, tag:string): Promise<void>
  addDashboardTag(id:string, tag:string): Promise<void>
  updateDashboardDescription(id:string, description:string): Promise<void>
}) {
  const dashboardData = await queryDashboardData(page, pageSize)
  const count = await queryDashboardDataCount()
  return <div className='p-4'>
    <div className='text-sm text-base-content text-right mb-2'>
      count: {count}
    </div>
    <DashboardShow
      initialDashboard={dashboardData}
      deleteDashboardData={deleteDashboardData}
      deleteDashboardTag={deleteDashboardTag}
      addDashboardTag={addDashboardTag}
      updateDashboardDescription={updateDashboardDescription}
    />
    <div className='flex justify-center mt-12'>
      <div className="join">
        <a href={`./dashboard?page=${page - 1}` }className={`join-item btn ${page <= 1 ? 'btn-disabled' : ''}`}>»</a>
        <button className="join-item btn">Page {page}</button>
        <a href={`./dashboard?page=${page + 1}` }className={`join-item btn ${page >= count / pageSize ? 'btn-disabled' : ''}`}>»</a>
      </div>
    </div>
  </div>
}
