'use client'

import { useState, useEffect, useRef } from 'react'
import { DashboardTag } from '@/app/type/dashboard_tag'
import { FaPlus, FaCheck, FaTimes } from 'react-icons/fa'

interface TagSelectorProps {
  availableTags: DashboardTag[]
  onTagSelect: (tagName: string) => void
  onCancel: () => void
}

export function TagSelector({ availableTags, onTagSelect, onCancel }: TagSelectorProps) {
  const [searchTerm, setSearchTerm] = useState('')
  const [isCreatingNew, setIsCreatingNew] = useState(false)
  const [newTagName, setNewTagName] = useState('')
  const inputRef = useRef<HTMLInputElement>(null)

  useEffect(() => {
    if (inputRef.current) {
      inputRef.current.focus()
    }
  }, [])

  const filteredTags = availableTags.filter(tag =>
    tag.name.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const exactMatch = availableTags.find(tag => 
    tag.name.toLowerCase() === searchTerm.toLowerCase()
  )

  const handleSelectTag = (tagName: string) => {
    onTagSelect(tagName)
    setSearchTerm('')
  }

  const handleCreateNew = () => {
    if (newTagName.trim()) {
      onTagSelect(newTagName.trim())
      setNewTagName('')
      setIsCreatingNew(false)
    }
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      if (isCreatingNew) {
        handleCreateNew()
      } else if (searchTerm.trim() && !exactMatch) {
        onTagSelect(searchTerm.trim())
        setSearchTerm('')
      } else if (filteredTags.length > 0) {
        handleSelectTag(filteredTags[0].name)
      }
    } else if (e.key === 'Escape') {
      onCancel()
    }
  }

  return (
    <div className="absolute z-10 bg-white border border-gray-300 rounded-md shadow-lg p-2 min-w-48">
      {!isCreatingNew ? (
        <>
          <input
            ref={inputRef}
            type="text"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            onKeyDown={handleKeyDown}
            className="input input-xs w-full mb-2"
            placeholder="搜索或输入新标签..."
          />
          
          <div className="max-h-32 overflow-y-auto">
            {filteredTags.map((tag) => (
              <div
                key={tag.id}
                onClick={() => handleSelectTag(tag.name)}
                className="px-2 py-1 hover:bg-gray-100 cursor-pointer rounded text-sm flex items-center gap-2"
              >
                <div
                  className="w-3 h-3 rounded-full border border-gray-400"
                  style={{ backgroundColor: tag.color || '#E3F2FD' }}
                ></div>
                <span className="text-gray-700">{tag.name}</span>
              </div>
            ))}
            
            {searchTerm.trim() && !exactMatch && (
              <div
                onClick={() => handleSelectTag(searchTerm.trim())}
                className="px-2 py-1 hover:bg-gray-100 cursor-pointer rounded text-sm flex items-center gap-2 border-t"
              >
                <FaPlus className="w-3 h-3 text-green-600" />
                创建 "{searchTerm.trim()}"
              </div>
            )}
          </div>
          
          <div className="flex gap-1 mt-2">
            <button
              onClick={() => setIsCreatingNew(true)}
              className="btn btn-xs btn-success flex-1"
            >
              <FaPlus className="w-3 h-3" />
              新建标签
            </button>
            <button
              onClick={onCancel}
              className="btn btn-xs btn-error"
            >
              <FaTimes className="w-3 h-3" />
            </button>
          </div>
        </>
      ) : (
        <div className="flex flex-col gap-2">
          <input
            type="text"
            value={newTagName}
            onChange={(e) => setNewTagName(e.target.value)}
            onKeyDown={(e) => {
              if (e.key === 'Enter') {
                handleCreateNew()
              } else if (e.key === 'Escape') {
                setIsCreatingNew(false)
                setNewTagName('')
              }
            }}
            className="input input-xs w-full"
            placeholder="输入新标签名称..."
            autoFocus
          />
          <div className="flex gap-1">
            <button
              onClick={handleCreateNew}
              className="btn btn-xs btn-success flex-1"
              disabled={!newTagName.trim()}
            >
              <FaCheck className="w-3 h-3" />
              创建
            </button>
            <button
              onClick={() => {
                setIsCreatingNew(false)
                setNewTagName('')
              }}
              className="btn btn-xs btn-error"
            >
              <FaTimes className="w-3 h-3" />
            </button>
          </div>
        </div>
      )}
    </div>
  )
}
