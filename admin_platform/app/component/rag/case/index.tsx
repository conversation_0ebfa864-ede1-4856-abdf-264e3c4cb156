'use client'
import SearchRag from './searchRag'
import { IElasticEmbeddingRes } from '../../../../../packages/model/elastic_search/elastic_search'
import AddRagSale from './addRag'

export function RagCase({
  addRag,
  searchRag,
  updateRag,
  deleteRag
}:{
  addRag(params: {
    industry:string
    description:string,
    caseClass:string
    formData:FormData;
  }): Promise<void>
  searchRag(params: {
    industry: string;
    number: number;
    minScore: number;
  }): Promise<IElasticEmbeddingRes[]>
  updateRag(params: {
    industry:string
    description:string
    caseClass:string
    images:string[]
    id: string
  }): Promise<void>
  deleteRag(id: string, images:string[]): Promise<void>
}) {
  return (
    <div className="px-16 py-16 pt-8">
      <h3 className="text-3xl leading-16">ElasticSearch sales case 管理</h3>
      <div className="tabs tabs-box mt-6 pt-[1rem] pr-[1rem] pb-[1rem] pl-[1rem]">
        <input
          type="radio"
          name="tabs"
          className="tab"
          aria-label="搜索"
          defaultChecked
        />
        <div className="tab-content bg-base-100 border-base-300 mt-[1rem] p-6">
          <SearchRag searchRag={searchRag} updateRag={updateRag} deleteRag={deleteRag}/>
        </div>

        <input type="radio" name="tabs" className="tab" aria-label="添加" />
        <div className="tab-content bg-base-100 border-base-300 mt-[1rem] p-6">
          <AddRagSale add={addRag} />
        </div>
      </div>
    </div>
  )
}