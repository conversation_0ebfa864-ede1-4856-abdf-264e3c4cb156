'use client'

import { toast } from 'react-toastify'
import { Dispatch, SetStateAction, useState } from 'react'
import { MdDeleteF<PERSON>ver, MdKeyboardArrowDown, MdOutlineCancel } from 'react-icons/md'
import { GoPencil } from 'react-icons/go'
import { IElasticEmbeddingRes } from '../../../../../packages/model/elastic_search/elastic_search'
import { RagContent } from '@/app/type/rag'
import Image from 'next/image'
export default function SearchCaseRag({
  searchRag,
  updateRag,
  deleteRag
}:{
  searchRag(params: {
    industry: string;
    number: number;
    minScore: number;
  }): Promise<IElasticEmbeddingRes[]>
  updateRag(params: {
    industry:string
    description:string,
    caseClass:string
    images:string[]
    id: string;
  }): Promise<void>
  deleteRag(id: string, images:string[]): Promise<void>
}) {
  const [alreadyQuery, setAlreadyQuery] = useState(false)
  const [loading, setLoading] = useState(false)
  const [queryResponse, setQueryResponse] = useState<IElasticEmbeddingRes[]>(
    [],
  )
  const [default_number, setDefaultNumber] = useState(0.8)
  const [isEditing, setIsEditing] = useState<Record<string, boolean>>({})
  const [editingData, setEditingData] = useState<Record<string, RagContent>>(
    {},
  )
  return (
    <div>
      <h3 className="text-xl">搜索语意</h3>
      <form
        className="flex items-center justify-between gap-8"
        onSubmit={() => {
          setLoading(true)
        }}
        action={(form) => {
          const industry = form.get('industry') as string
          const number = Number(form.get('number') as string)
          const minScore = Number(form.get('min_score') as string)
          toast
            .promise(searchRag({ industry, number, minScore }), {
              pending: 'query is pending',
              success: 'query resolved 👌',
              error: 'query rejected 🤯',
            })
            .then((res) => {
              setQueryResponse(res)
              setIsEditing({})
              setEditingData({})
            })
            .finally(() => {
              setAlreadyQuery(true)
              setLoading(false)
            })
        }}
      >
        <fieldset className="fieldset relative max-w-1/2 flex-auto">
          <legend className="fieldset-legend truncate">输入行业进行搜索</legend>
          <input
            type="text"
            className="input validator w-full focus-within:outline-0"
            placeholder="Type here"
            name="industry"
            required
          />
          <div className="validator-hint absolute -bottom-4">
            Enter valid question
          </div>
        </fieldset>
        <div className="flex items-center gap-8">
          <fieldset className="fieldset">
            <legend className="fieldset-legend truncate">最小分数</legend>
            <input
              step="any"
              type="number"
              name="min_score"
              defaultValue={default_number}
              onChange={(e) => {
                setDefaultNumber(Number(e.currentTarget.value))
              }}
              min={0}
              max={1}
              className="input w-16 focus-within:outline-0"
            />
          </fieldset>
          <fieldset className="fieldset">
            <legend className="fieldset-legend truncate">返回的结果数</legend>
            <input
              type="number"
              name="number"
              defaultValue={10}
              className="input w-16 focus-within:outline-0"
            />
          </fieldset>
          <button
            type="submit"
            className="btn btn-neutral relative bottom-1 self-end"
            disabled={loading}
          >
            搜索
          </button>
        </div>
      </form>
      {alreadyQuery && (
        <QueryResponse
          isEditing={isEditing}
          setIsEditing={setIsEditing}
          editingData={editingData}
          setEditingData={setEditingData}
          loading={loading}
          queryResponse={queryResponse}
          setLoading={setLoading}
          setQueryResponse={setQueryResponse}
          updateRag={updateRag}
          deleteRag={deleteRag}
        />
      )}
    </div>
  )
}

function QueryResponse({
  queryResponse,
  loading,
  setLoading,
  setQueryResponse,
  isEditing,
  setIsEditing,
  updateRag,
  deleteRag
}: {
  queryResponse: IElasticEmbeddingRes[];
  loading: boolean;
  setLoading: Dispatch<SetStateAction<boolean>>;
  setQueryResponse: Dispatch<SetStateAction<IElasticEmbeddingRes[]>>;
  isEditing: Record<string, boolean>;
  setIsEditing: Dispatch<SetStateAction<Record<string, boolean>>>;
  editingData: Record<string, RagContent>;
  setEditingData: Dispatch<SetStateAction<Record<string, RagContent>>>;
  updateRag(params: {
    industry:string
    description:string
    caseClass:string
    images:string[]
    id: string
  }): Promise<void>
  deleteRag(id: string, images:string[]): Promise<void>
}) {
  return (
    <div>
      <div className="my-8 text-xl">
        搜索结果
        {loading && (
          <span className="loading loading-spinner loading-md ml-4"></span>
        )}
      </div>
      {queryResponse.length == 0 && <div>查询结果为空</div>}
      <div className="flex flex-col gap-4">
        {queryResponse.map((res, index) => {
          const id = res.id
          const score: number = res.score
          const industry = res.metadata['topic']
          const description = res.metadata['description']
          const caseClass = res.metadata['doc']
          const images = JSON.parse(res.metadata['images']) as string[]
          return (
            <div
              key={index}
              className="flex items-center justify-between gap-4"
            >
              <div className="bg-base-100 border-base-300 collapse border">
                <input type="checkbox" defaultChecked />
                <div className="collapse-title flex items-center justify-between font-semibold">
                  <div>{`#${index + 1} 行业类目：${industry}`}</div>
                  <MdKeyboardArrowDown size={28} />
                </div>
                <div className="collapse-content flex flex-col gap-4 text-sm">
                  <div>
                    <span>描叙：</span>
                    <span>{`${description}`}</span>
                  </div>
                  <div>
                    <span>案例类型</span>
                    <span>{`${caseClass}`}</span>
                  </div>
                  <div>
                    <span>images</span>
                    <div className='flex gap-2 flex-wrap'>{images.map((img, index) => <Image key={index} alt="img"
                      width={800}
                      height={600}
                      style={{ height: '300px', width: 'auto' }} src={img}/>)}
                    </div>
                  </div>
                  <div>
                    <span>相似度分数：</span>
                    <span>{`${score == -1 ? 'update when query again' : score}`}</span>
                  </div>
                  {isEditing[res.id] && (
                    <form
                      className="flex flex-col gap-4"
                      action={(form) => {
                        const industry = form.get('industry') as string
                        const description = form.get('description') as string
                        const caseClass = form.get('classification') as string
                        setLoading(true)
                        toast
                          .promise(
                            updateRag({ industry, description, caseClass, id: res.id, images:images }),
                            {
                              pending: 'update is pending',
                              success: 'update resolved 👌',
                              error: 'update rejected 🤯',
                            },
                          )
                          .then(() => {
                            if (industry != queryResponse[index].metadata['topic']) {
                              queryResponse[index].score = -1
                            }
                            queryResponse[index].metadata['topic'] = industry
                            queryResponse[index].metadata['description'] = description
                            queryResponse[index].metadata['doc'] = caseClass
                            setQueryResponse([...queryResponse])
                          })
                          .then(() => {
                            setIsEditing({
                              ...isEditing,
                              [res.id]: false,
                            })
                          })
                          .finally(() => {
                            setLoading(false)
                          })
                      }}
                    >
                      <div className="flex items-center gap-4">
                        <span className='w-24'>新行业类目：</span>
                        <textarea
                          className="textarea w-[40rem] focus-within:outline-0"
                          placeholder="industry"
                          defaultValue={industry}
                          name="industry"
                        ></textarea>
                      </div>
                      <div className="flex items-center gap-4">
                        <span className='w-24'>新描述：</span>
                        <textarea
                          className="textarea w-[40rem] focus-within:outline-0"
                          placeholder="description"
                          defaultValue={description}
                          name="description"
                        ></textarea>
                      </div>
                      <div className="flex items-center gap-4">
                        <span className='w-24'>新案例分类：</span>
                        <textarea
                          className="textarea w-[40rem] focus-within:outline-0"
                          placeholder="case class"
                          defaultValue={caseClass}
                          name="classification"
                        ></textarea>
                      </div>
                      <button
                        type="submit"
                        className="btn btn-neutral relative bottom-1 mr-20 self-end"
                        disabled={loading}
                      >
                        修改
                      </button>
                    </form>
                  )}
                </div>
              </div>
              <div className="flex flex-col gap-6">
                <button
                  className="btn btn-neutral btn-square disabled:btn-disabled"
                  disabled={loading}
                  onClick={() => {
                    setLoading(true)
                    toast
                      .promise(deleteRag(id, images), {
                        pending: 'delete is pending',
                        success: 'delete resolved 👌',
                        error: 'delete rejected 🤯',
                      })
                      .then(() => {
                        setQueryResponse((queryResponse) => {
                          return queryResponse.filter((item) => {
                            return item.id != res.id
                          })
                        })
                      })
                      .finally(() => {
                        setLoading(false)
                      })
                  }}
                >
                  <MdDeleteForever size={24} />
                </button>
                {isEditing[res.id] ? (
                  <div
                    className="btn btn-neutral btn-square"
                    onClick={() => {
                      setIsEditing({
                        ...isEditing,
                        [res.id]: false,
                      })
                    }}
                  >
                    <MdOutlineCancel size={24} />
                  </div>
                ) : (
                  <div
                    className="btn btn-neutral btn-square"
                    onClick={() => {
                      setIsEditing({
                        ...isEditing,
                        [res.id]: true,
                      })
                    }}
                  >
                    <GoPencil size={24} />
                  </div>
                )}
              </div>
            </div>
          )
        })}
      </div>
    </div>
  )
}
