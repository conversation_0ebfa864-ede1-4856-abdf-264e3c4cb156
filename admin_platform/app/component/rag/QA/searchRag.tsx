'use client'

import { toast } from 'react-toastify'
import { Dispatch, SetStateAction, useState } from 'react'
import { MdDeleteF<PERSON>ver, MdKeyboardArrowDown, MdOutlineCancel } from 'react-icons/md'
import { GoPencil } from 'react-icons/go'
import { RagContent } from '@/app/type/rag'
import { IElasticEmbeddingRes } from '../../../../../packages/model/elastic_search/elastic_search'
export default function SearchRag({
  ragDoc,
  industryTag,
  searchRag,
  updateRag,
  deleteRag
}:{
  ragDoc: Record<string, string>
  industryTag: Record<string, string>
  searchRag({ question, number, minScore }: {
      question: string;
      number: number;
      minScore: number;
  }): Promise<IElasticEmbeddingRes[]>
  updateRag({ question, answer, tag, doc, id, }: {
      question: string;
      answer: string;
      tag: string;
      doc: string;
      id: string;
  }): Promise<void>
  deleteRag(id: string, resourceNames: string[]): Promise<void>
}) {
  const [alreadyQuery, setAlreadyQuery] = useState(false)
  const [loading, setLoading] = useState(false)
  const [queryResponse, setQueryResponse] = useState<IElasticEmbeddingRes[]>(
    [],
  )
  const [default_number, setDefaultNumber] = useState(0.8)
  const [isEditing, setIsEditing] = useState<Record<string, boolean>>({})
  const [editingData, setEditingData] = useState<Record<string, RagContent>>(
    {},
  )
  return (
    <div>
      <h3 className="text-xl">搜索语意</h3>
      <form
        className="flex items-center justify-between gap-8"
        onSubmit={() => {
          setLoading(true)
        }}
        action={(form) => {
          const question = form.get('question') as string
          const number = Number(form.get('number') as string)
          const minScore = Number(form.get('min_score') as string)
          toast
            .promise(searchRag({ question, number, minScore }), {
              pending: 'query is pending',
              success: 'query resolved 👌',
              error: 'query rejected 🤯',
            })
            .then((res) => {
              setQueryResponse(res)
              setIsEditing({})
              setEditingData({})
            })
            .finally(() => {
              setAlreadyQuery(true)
              setLoading(false)
            })
        }}
      >
        <fieldset className="fieldset relative max-w-1/2 flex-auto">
          <legend className="fieldset-legend truncate">输入问题进行搜索</legend>
          <input
            type="text"
            className="input validator w-full focus-within:outline-0"
            placeholder="Type here"
            name="question"
            required
          />
          <div className="validator-hint absolute -bottom-4">
            Enter valid question
          </div>
        </fieldset>
        <div className="flex items-center gap-8">
          <fieldset className="fieldset">
            <legend className="fieldset-legend truncate">最小分数</legend>
            <input
              step="any"
              type="number"
              name="min_score"
              defaultValue={default_number}
              onChange={(e) => {
                setDefaultNumber(Number(e.currentTarget.value))
              }}
              min={0}
              max={1}
              className="input w-16 focus-within:outline-0"
            />
          </fieldset>
          <fieldset className="fieldset">
            <legend className="fieldset-legend truncate">返回的结果数</legend>
            <input
              type="number"
              name="number"
              defaultValue={10}
              className="input w-16 focus-within:outline-0"
            />
          </fieldset>
          <button
            type="submit"
            className="btn btn-neutral relative bottom-1 self-end"
            disabled={loading}
          >
            搜索
          </button>
        </div>
      </form>
      {alreadyQuery && (
        <QueryResponse
          isEditing={isEditing}
          setIsEditing={setIsEditing}
          editingData={editingData}
          setEditingData={setEditingData}
          loading={loading}
          queryResponse={queryResponse}
          setLoading={setLoading}
          setQueryResponse={setQueryResponse}
          ragDoc={ragDoc}
          industryTag={industryTag}
          updateRag={updateRag}
          deleteRag={deleteRag}
        />
      )}
    </div>
  )
}

function QueryResponse({
  queryResponse,
  loading,
  setLoading,
  setQueryResponse,
  isEditing,
  setIsEditing,
  ragDoc,
  industryTag,
  updateRag,
  deleteRag
}: {
  queryResponse: IElasticEmbeddingRes[];
  loading: boolean;
  setLoading: Dispatch<SetStateAction<boolean>>;
  setQueryResponse: Dispatch<SetStateAction<IElasticEmbeddingRes[]>>;
  isEditing: Record<string, boolean>;
  setIsEditing: Dispatch<SetStateAction<Record<string, boolean>>>;
  editingData: Record<string, RagContent>;
  setEditingData: Dispatch<SetStateAction<Record<string, RagContent>>>;
  ragDoc: Record<string, string>
  industryTag: Record<string, string>
  updateRag({ question, answer, tag, doc, id, }: {
      question: string;
      answer: string;
      tag: string;
      doc: string;
      id: string;
  }): Promise<void>
  deleteRag(id: string, resourceNames: string[]): Promise<void>
}) {
  return (
    <div>
      <div className="my-8 text-xl">
        搜索结果
        {loading && (
          <span className="loading loading-spinner loading-md ml-4"></span>
        )}
      </div>
      {queryResponse.length == 0 && <div>查询结果为空</div>}
      <div className="flex flex-col gap-4">
        {queryResponse.map((res, index) => {
          const question: string = res.metadata['q'] ?? ''
          const answer: string = res.metadata['a'] ?? ''
          const doc: string = res.metadata['doc'] ?? ''
          const tag: string = res.metadata['tag'] ?? '无'
          const score: number = res.score
          return (
            <div
              key={index}
              className="flex items-center justify-between gap-4"
            >
              <div className="bg-base-100 border-base-300 collapse border">
                <input type="checkbox" defaultChecked />
                <div className="collapse-title flex items-center justify-between font-semibold">
                  <div>{`#${index + 1} 问题：${question}`}</div>
                  <MdKeyboardArrowDown size={28} />
                </div>
                <div className="collapse-content flex flex-col gap-4 text-sm">
                  <div>
                    <span>答案：</span>
                    <span>{`${answer}`}</span>
                  </div>
                  <div>
                    <span>文档：</span>
                    <span>{`${doc}`}</span>
                  </div>
                  <div>
                    <span>行业标签：</span>
                    <span>{`${tag}`}</span>
                  </div>
                  <div>
                    <span>相似度分数：</span>
                    <span>{`${score == -1 ? 'update when query again' : score}`}</span>
                  </div>
                  {isEditing[res.id] && (
                    <form
                      className="flex flex-col gap-4"
                      action={(form) => {
                        const question = form.get('question') as string
                        const doc = form.get('doc') as string
                        const tag = form.get('tag') as string
                        const answer = form.get('answer') as string
                        setLoading(true)
                        toast
                          .promise(
                            updateRag({ question, answer, doc, tag, id: res.id }),
                            {
                              pending: 'update is pending',
                              success: 'update resolved 👌',
                              error: 'update rejected 🤯',
                            },
                          )
                          .then(() => {
                            setQueryResponse(
                              queryResponse.map((item) => {
                                if (item.id == res.id) {
                                  if (question != item.metadata['q']) {
                                    item.score = -1
                                  }
                                  item.metadata['q'] = question
                                  item.metadata['a'] = answer
                                  item.metadata['doc'] = doc
                                }
                                return item
                              }),
                            )
                          })
                          .then(() => {
                            setIsEditing({
                              ...isEditing,
                              [res.id]: false,
                            })
                          })
                          .finally(() => {
                            setLoading(false)
                          })
                      }}
                    >
                      <div className="flex items-center gap-4">
                        <span>新问题：</span>
                        <textarea
                          className="textarea w-[40rem] focus-within:outline-0"
                          placeholder="question"
                          defaultValue={question}
                          name="question"
                        ></textarea>
                      </div>
                      <div className="flex items-center gap-4">
                        <span>新答案：</span>
                        <textarea
                          className="textarea w-[40rem] focus-within:outline-0"
                          placeholder="question"
                          defaultValue={answer}
                          name="answer"
                        ></textarea>
                      </div>
                      <div className="flex items-center gap-4">
                        <span>新标签：</span>
                        <select
                          defaultValue={tag}
                          className="select focus-within:outline-0"
                          name="tag"
                        >
                          <option disabled={true}>Pick a doc</option>
                          {Object.entries(industryTag).map(([k, v], index) => (
                            <option key={index} value={v}>
                              {k}
                            </option>
                          ))}
                        </select>
                      </div>
                      <div className="flex items-center gap-4">
                        <span>新文档：</span>
                        <select
                          defaultValue={doc}
                          className="select focus-within:outline-0"
                          name="doc"
                        >
                          <option disabled={true}>Pick a doc</option>
                          {Object.entries(ragDoc).map(([k, v], index) => (
                            <option key={index} value={v}>
                              {k}
                            </option>
                          ))}
                        </select>
                      </div>
                      <button
                        type="submit"
                        className="btn btn-neutral relative bottom-1 mr-20 self-end"
                        disabled={loading}
                      >
                        修改
                      </button>
                    </form>
                  )}
                </div>
              </div>
              <div className="flex flex-col gap-6">
                <button
                  className="btn btn-neutral btn-square disabled:btn-disabled"
                  disabled={loading}
                  onClick={() => {
                    setLoading(true)
                    const regex = /\[(.*?)\]/g
                    const matches = [...answer.matchAll(regex)].map(
                      (m) => m[1],
                    )
                    toast
                      .promise(deleteRag(res.id, matches), {
                        pending: 'delete is pending',
                        success: 'delete resolved 👌',
                        error: 'delete rejected 🤯',
                      })
                      .then(() => {
                        setQueryResponse((queryResponse) => {
                          return queryResponse.filter((item) => {
                            return item.id != res.id
                          })
                        })
                      })
                      .finally(() => {
                        setLoading(false)
                      })
                  }}
                >
                  <MdDeleteForever size={24} />
                </button>
                {isEditing[res.id] ? (
                  <div
                    className="btn btn-neutral btn-square"
                    onClick={() => {
                      setIsEditing({
                        ...isEditing,
                        [res.id]: false,
                      })
                    }}
                  >
                    <MdOutlineCancel size={24} />
                  </div>
                ) : (
                  !answer.includes('[') &&
                  !answer.includes(']') && (
                    <div
                      className="btn btn-neutral btn-square"
                      onClick={() => {
                        setIsEditing({
                          ...isEditing,
                          [res.id]: true,
                        })
                      }}
                    >
                      <GoPencil size={24} />
                    </div>
                  )
                )}
              </div>
            </div>
          )
        })}
      </div>
    </div>
  )
}
