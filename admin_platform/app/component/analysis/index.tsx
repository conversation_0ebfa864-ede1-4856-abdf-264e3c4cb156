'use client'
import dayjs from 'dayjs'
import { useEffect, useMemo, useState } from 'react'
import { toast } from 'react-toastify'
import { TiTick } from 'react-icons/ti'
import { RxCross2 } from 'react-icons/rx'
import {
  MaterialReactTable,
  useMaterialReactTable,
  type MRT_ColumnDef
} from 'material-react-table'
import { AnalysisData } from '@/app/type/analysis'
import Link from 'next/link'

export function Analysis({ getAnalysisData, chatHistoryPageLinkPrefix }:{getAnalysisData:(startCourseNo:number, endCourseNo:number)=>Promise<AnalysisData[]>, chatHistoryPageLinkPrefix:string }) {
  const [startCourseNo, setStartCourseNo] = useState<number>(Number(dayjs().subtract(7, 'day').format('YYYYMMDD')))
  const [columnFilters, setColumnFilters] = useState<ColumnFilter[]>([])
  const [endCourseNo, setEndCourseNo] = useState<number>(Number(dayjs().format('YYYYMMDD')))
  const [loading, setLoading] = useState<boolean>(true)
  const [data, setData] = useState<AnalysisData[]>([])
  const [filterData, setFilterData] = useState<AnalysisData[]>([])
  useEffect(() => {
    query(startCourseNo, endCourseNo)
  }, [])
  useEffect(() => {
    setFilterData(table.getFilteredRowModel().rows.map((item) => item.original))
  }, [columnFilters])
  const columns = useMemo<MRT_ColumnDef<AnalysisData>[]>(() => [
    {
      accessorKey: 'chatId',
      header:'chat_id',
      size:150,
    },
    {
      accessorKey: 'name',
      header:'昵称',
      size:150,
      enableHiding:true
    },
    {
      accessorKey:'courseNo',
      header: '期数',
      size:150
    },
    {
      accessorKey:'courseNoOri',
      header: '原期数',
      size:150,
      Cell: ({ cell }) => cell.getValue<number>() == 0 ? '' : cell.getValue<number>()
    },
    {
      accessorKey:'assistant',
      header:'助教',
      size:150
    },
    {
      accessorKey:'phone',
      header:'电话',
      size:150
    },
    {
      accessorKey:'ip',
      header:'ip',
      size:150
    },
    {
      accessorKey:'chatNumber',
      header: '客户发言次数',
      size:150
    },
    {
      accessorKey:'isFillAnyUserSlots',
      header: '填写了一点点客户画像',
      size:50,
      filterVariant:'select',
      filterSelectOptions:[{ label:'是', value:'true' }, { label:'否', value:'false' }],
      Cell: ({ cell }) => cell.getValue<boolean>() ? <TiTick /> : <RxCross2 />
    },
    {
      accessorKey:'isFillUserSlots',
      header: '填写了客户画像',
      size:50,
      filterVariant:'select',
      filterSelectOptions:[{ label:'是', value:'true' }, { label:'否', value:'false' }],
      Cell: ({ cell }) => cell.getValue<boolean>() ? <TiTick /> : <RxCross2 />
    },
    {
      accessorKey:'isCompleteDouyinAnalysis',
      header: '抖音截图分析',
      size:50,
      filterVariant:'select',
      filterSelectOptions:[{ label:'是', value:'true' }, { label:'否', value:'false' }],
      Cell: ({ cell }) => cell.getValue<boolean>() ? <TiTick /> : <RxCross2 />
    },
    {
      accessorKey:'isCompleteHomework1',
      header: '第一节课作业',
      size:50,
      filterVariant:'select',
      filterSelectOptions:[{ label:'是', value:'true' }, { label:'否', value:'false' }],
      Cell: ({ cell }) => cell.getValue<boolean>() ? <TiTick /> : <RxCross2 />
    },
    {
      accessorKey:'isCompleteHomework2',
      header: '第二节课作业',
      size:50,
      filterVariant:'select',
      filterSelectOptions:[{ label:'是', value:'true' }, { label:'否', value:'false' }],
      Cell: ({ cell }) => cell.getValue<boolean>() ? <TiTick /> : <RxCross2 />
    },
    {
      accessorKey:'isCompleteHomework3',
      header: '第三节课作业',
      size:50,
      filterVariant:'select',
      filterSelectOptions:[{ label:'是', value:'true' }, { label:'否', value:'false' }],
      Cell: ({ cell }) => cell.getValue<boolean>() ? <TiTick /> : <RxCross2 />
    },
    {
      accessorKey:'isAttendCourseDay1',
      header: '第一节课到课',
      size:50,
      filterVariant:'select',
      filterSelectOptions:[{ label:'是', value:'true' }, { label:'否', value:'false' }],
      Cell: ({ cell }) => cell.getValue<boolean>() ? <TiTick /> : <RxCross2 />
    },
    {
      accessorKey:'isAttendCourseDay2',
      header: '第二节课到课',
      size:50,
      filterVariant:'select',
      filterSelectOptions:[{ label:'是', value:'true' }, { label:'否', value:'false' }],
      Cell: ({ cell }) => cell.getValue<boolean>() ? <TiTick /> : <RxCross2 />
    },
    {
      accessorKey:'isAttendCourseDay3',
      header: '第三节课到课',
      size:50,
      filterVariant:'select',
      filterSelectOptions:[{ label:'是', value:'true' }, { label:'否', value:'false' }],
      Cell: ({ cell }) => cell.getValue<boolean>() ? <TiTick /> : <RxCross2 />
    },
    {
      accessorKey:'isAttendCourseDay4',
      header: '第四节课到课',
      size:50,
      filterVariant:'select',
      filterSelectOptions:[{ label:'是', value:'true' }, { label:'否', value:'false' }],
      Cell: ({ cell }) => cell.getValue<boolean>() ? <TiTick /> : <RxCross2 />
    },
    {
      accessorKey:'isCompleteCourseDay1',
      header: '第一节课完课',
      size:50,
      filterVariant:'select',
      filterSelectOptions:[{ label:'是', value:'true' }, { label:'否', value:'false' }],
      Cell: ({ cell }) => cell.getValue<boolean>() ? <TiTick /> : <RxCross2 />
    },
    {
      accessorKey:'isCompleteCourseDay2',
      header: '第二节课完课',
      size:50,
      filterVariant:'select',
      filterSelectOptions:[{ label:'是', value:'true' }, { label:'否', value:'false' }],
      Cell: ({ cell }) => cell.getValue<boolean>() ? <TiTick /> : <RxCross2 />
    },
    {
      accessorKey:'isCompleteCourseDay3',
      header: '第三节课完课',
      size:50,
      filterVariant:'select',
      filterSelectOptions:[{ label:'是', value:'true' }, { label:'否', value:'false' }],
      Cell: ({ cell }) => cell.getValue<boolean>() ? <TiTick /> : <RxCross2 />
    },
    {
      accessorKey:'isCompleteCourseDay4',
      header: '第四节课完课',
      size:50,
      filterVariant:'select',
      filterSelectOptions:[{ label:'是', value:'true' }, { label:'否', value:'false' }],
      Cell: ({ cell }) => cell.getValue<boolean>() ? <TiTick /> : <RxCross2 />
    },
    {
      accessorKey:'isInviteGroupFailAfterPayment',
      header: '下单后是否拉群成功',
      size:50,
      filterVariant:'select',
      filterSelectOptions:[{ label:'是', value:'true' }, { label:'否', value:'false' }],
      Cell: ({ cell }) => cell.getValue<boolean>() ? <TiTick /> : <RxCross2 />
    },
    {
      accessorKey:'isPaid',
      header: '下单',
      size:50,
      filterVariant:'select',
      filterSelectOptions:[{ label:'是', value:'true' }, { label:'否', value:'false' }],
      Cell: ({ cell }) => cell.getValue<boolean>() ? <TiTick /> : <RxCross2 />
    },
    {
      accessorKey:'paidTime',
      header: '下单时间',
      size:50,
      Cell: ({ cell }) => {
        const value = cell.getValue<Date | null>()
        if (!value) {
          return <div></div>
        }
        return <div>{dayjs(value).format('YYYY-MM-DD HH:mm:ss')}</div>
      }
    },
  ], [])
  const table = useMaterialReactTable({
    columns,
    data, //data must be memoized or stable (useState, useMemo, defined outside of this component, etc.)
    state:{
      columnFilters
    },
    onColumnFiltersChange: setColumnFilters,
    enableColumnPinning: true,
    enableRowActions: true,
    renderRowActions: ({ row }) => (
      <Link href={`${chatHistoryPageLinkPrefix}/${row.getValue('chatId')}`}><button className='btn btn-info btn-soft btn-sm m-2'>jump</button></Link>
    ),
    initialState:{
      pagination:{ pageSize:50, pageIndex:0 }, density:'compact',
      columnPinning:{
        left:['name', 'courseNo', 'assistant']
      },
      columnVisibility:{
        chatId:false
      },
      sorting: [
        {
          id: 'chatNumber', //sort by age by default on page load
          desc: true,
        },
      ],
    }
  })
  const query = (startCourseNo:number, endCourseNo:number) => {
    setLoading(true)
    toast.promise(getAnalysisData(startCourseNo, endCourseNo), {
      pending:'query pending',
      success:'query success',
      error: 'query error'
    }).then((result) => {
      setData(result)
      setFilterData(result)
    }).finally(() => {
      setLoading(false)
    })
  }
  if (loading) {
    return <>loading</>
  }
  return <div className='m-2'>
    <h2 className='text-3xl p-2 font-semibold'>analysis</h2>
    <div className='flex gap-2 p-1'>
      <label className='label w-30'>start course no:</label>
      <input type="number" className='input' value={startCourseNo} onChange={(e) => { setStartCourseNo(Number(e.currentTarget.value)) }}/>
      <button className='btn btn-neutral disabled:btn-disabled' disabled={loading} onClick={() => {
        let text = '昵称,手机号,期数,助教,客户发言次数,填写了一点点客户画像,填写了客户画像,抖音截图分析,第一节课作业,第二节课作业,第三节课作业,第一节课到课,第二节课到课,第三节课到课,第四节课到课,第一节课完课,第二节课完课,第三节课完课,第四节课完课,下单\n'
        for (const user of filterData) {
          text += `${user.name},${user.phone},${user.courseNo},${user.assistant},${user.chatNumber},${user.isFillAnyUserSlots},${user.isFillUserSlots},${user.isCompleteDouyinAnalysis},${user.isCompleteHomework1},${user.isCompleteHomework2},${user.isCompleteHomework3},${user.isAttendCourseDay1},${user.isAttendCourseDay2},${user.isAttendCourseDay3},${user.isAttendCourseDay4},${user.isCompleteCourseDay1},${user.isCompleteCourseDay2},${user.isCompleteCourseDay3},${user.isCompleteCourseDay4},${user.isPaid}\n`
        }
        const blob = new Blob([text], { type: 'text/plain' })

        // 2. 创建临时链接
        const link = document.createElement('a')
        link.href = URL.createObjectURL(blob)
        link.download = 'analysis.csv'

        // 3. 触发下载
        document.body.appendChild(link)
        link.click()

        // 4. 清理
        document.body.removeChild(link)
        URL.revokeObjectURL(link.href)
      }}>export</button>
    </div>
    <div className='flex gap-2 p-1'>
      <label className='label w-30'>end course no:</label>
      <input type="number" className='input' value={endCourseNo} onChange={(e) => { setEndCourseNo(Number(e.currentTarget.value)) }} />
      <button className='btn btn-neutral disabled:btn-disabled' disabled={loading} onClick={() => {
        query(startCourseNo, endCourseNo)
      }}>query</button>
    </div>
    <div className='flex gap-x-12 gap-y-4 py-4 justify-start flex-wrap'>
      <div className='shadow stats'>
        <div className='stat'>
          <div className='stat-title'>下单率</div>
          <div className='stat-value'>{(filterData.filter((item) => item.isPaid).length / filterData.length * 100).toFixed(2)}%</div>
          <div className='stat-desc'>{filterData.filter((item) => item.isPaid).length} / {filterData.length}</div>
        </div>
      </div>
      <div className='shadow stats'>
        <div className='stat'>
          <div className='stat-title'>平均对话轮数</div>
          <div className='stat-value'>{(filterData.map((item) => item.chatNumber).reduce((pre, cur) => pre + cur, 0) / filterData.length).toFixed(2)}</div>
          <div className='stat-desc'>{filterData.map((item) => item.chatNumber).reduce((pre, cur) => pre + cur, 0)} / {filterData.length}</div>
        </div>
      </div>
      <div className='shadow stats'>
        <div className='stat'>
          <div className='stat-title'>填写了一点点客户画像</div>
          <div className='stat-value'>{(filterData.filter((item) => item.isFillAnyUserSlots).length / filterData.length * 100).toFixed(2)}%</div>
          <div className='stat-desc'>{filterData.filter((item) => item.isFillAnyUserSlots).length} / {filterData.length}</div>
        </div>
        <div className='stat'>
          <div className='stat-title'>填写了客户画像</div>
          <div className='stat-value'>{(filterData.filter((item) => item.isFillUserSlots).length / filterData.length * 100).toFixed(2)}%</div>
          <div className='stat-desc'>{filterData.filter((item) => item.isFillUserSlots).length} / {filterData.length}</div>
        </div>
      </div>
      <div className='shadow stats'>
        <div className='stat'>
          <div className='stat-title'>抖音截图诊断</div>
          <div className='stat-value'>{(filterData.filter((item) => item.isCompleteDouyinAnalysis).length / filterData.length * 100).toFixed(2)}%</div>
          <div className='stat-desc'>{filterData.filter((item) => item.isCompleteDouyinAnalysis).length} / {filterData.length}</div>
        </div>
        <div className='stat'>
          <div className='stat-title'>第一课作业</div>
          <div className='stat-value'>{(filterData.filter((item) => item.isCompleteHomework1).length / filterData.length * 100).toFixed(2)}%</div>
          <div className='stat-desc'>{filterData.filter((item) => item.isCompleteHomework1).length} / {filterData.length}</div>
        </div>
        <div className='stat'>
          <div className='stat-title'>第二课作业</div>
          <div className='stat-value'>{(filterData.filter((item) => item.isCompleteHomework2).length / filterData.length * 100).toFixed(2)}%</div>
          <div className='stat-desc'>{filterData.filter((item) => item.isCompleteHomework2).length} / {filterData.length}</div>
        </div>
        <div className='stat'>
          <div className='stat-title'>第三课作业</div>
          <div className='stat-value'>{(filterData.filter((item) => item.isCompleteHomework3).length / filterData.length * 100).toFixed(2)}%</div>
          <div className='stat-desc'>{filterData.filter((item) => item.isCompleteHomework3).length} / {filterData.length}</div>
        </div>
      </div>
      <div className='stats shadow'>
        <div className='stat'>
          <div className='stat-title'>第一节课到课</div>
          <div className='stat-value'>{(filterData.filter((item) => item.isAttendCourseDay1).length / filterData.length * 100).toFixed(2)}%</div>
          <div className='stat-desc'>{filterData.filter((item) => item.isAttendCourseDay1).length} / {filterData.length}</div>
        </div>
        <div className='stat'>
          <div className='stat-title'>第二节课到课</div>
          <div className='stat-value'>{(filterData.filter((item) => item.isAttendCourseDay2).length / filterData.length * 100).toFixed(2)}%</div>
          <div className='stat-desc'>{filterData.filter((item) => item.isAttendCourseDay2).length} / {filterData.length}</div>
        </div>
        <div className='stat'>
          <div className='stat-title'>第三节课到课</div>
          <div className='stat-value'>{(filterData.filter((item) => item.isAttendCourseDay3).length / filterData.length * 100).toFixed(2)}%</div>
          <div className='stat-desc'>{filterData.filter((item) => item.isAttendCourseDay3).length} / {filterData.length}</div>
        </div>
        <div className='stat'>
          <div className='stat-title'>第四节课到课</div>
          <div className='stat-value'>{(filterData.filter((item) => item.isAttendCourseDay4).length / filterData.length * 100).toFixed(2)}%</div>
          <div className='stat-desc'>{filterData.filter((item) => item.isAttendCourseDay4).length} / {filterData.length}</div>
        </div>
      </div>
      <div className='stats shadow'>
        <div className='stat'>
          <div className='stat-title'>第一节课完课</div>
          <div className='stat-value'>{(filterData.filter((item) => item.isCompleteCourseDay1).length / filterData.length * 100).toFixed(2)}%</div>
          <div className='stat-desc'>{filterData.filter((item) => item.isCompleteCourseDay1).length} / {filterData.length}</div>
        </div>
        <div className='stat'>
          <div className='stat-title'>第二节课完课</div>
          <div className='stat-value'>{(filterData.filter((item) => item.isCompleteCourseDay2).length / filterData.length * 100).toFixed(2)}%</div>
          <div className='stat-desc'>{filterData.filter((item) => item.isCompleteCourseDay2).length} / {filterData.length}</div>
        </div>
        <div className='stat'>
          <div className='stat-title'>第三节课完课</div>
          <div className='stat-value'>{(filterData.filter((item) => item.isCompleteCourseDay3).length / filterData.length * 100).toFixed(2)}%</div>
          <div className='stat-desc'>{filterData.filter((item) => item.isCompleteCourseDay3).length} / {filterData.length}</div>
        </div>
        <div className='stat'>
          <div className='stat-title'>第四节课完课</div>
          <div className='stat-value'>{(filterData.filter((item) => item.isCompleteCourseDay4).length / filterData.length * 100).toFixed(2)}%</div>
          <div className='stat-desc'>{filterData.filter((item) => item.isCompleteCourseDay4).length} / {filterData.length}</div>
        </div>
      </div>
    </div>
    <div className='flex justify-center'>
      <div className='max-w-[85dvw]'>
        <MaterialReactTable table={table} />
      </div>
    </div>
  </div>
}

interface ColumnFilter {
  id: string
  value: unknown
}