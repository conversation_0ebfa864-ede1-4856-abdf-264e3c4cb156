export interface AnalysisData {
  chatId:string
  name: string,
  assistant: string,
  chatNumber: number
  courseNo:number
  courseNoOri:number
  isPaid: boolean
  phone: string
  ip: string
  paidTime:Date | null
  isInviteGroupFailAfterPayment:boolean

  isAttendCourseDay1: boolean
  isAttendCourseDay2: boolean
  isAttendCourseDay3: boolean
  isAttendCourseDay4: boolean

  isCompleteCourseDay1: boolean
  isCompleteCourseDay2: boolean
  isCompleteCourseDay3: boolean
  isCompleteCourseDay4: boolean

  isCompleteDouyinAnalysis: boolean
  isCompleteHomework1: boolean
  isCompleteHomework2: boolean
  isCompleteHomework3: boolean

  isFillAnyUserSlots: boolean
  isFillUserSlots: boolean
}